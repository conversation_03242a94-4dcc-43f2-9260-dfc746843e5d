import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'habit.dart';
import 'section.dart';
import 'section_color_palette.dart';
import 'modern_theme.dart';

class ModernHabitDetailsModal extends StatelessWidget {
  final Habit habit;
  final List<Section> sections;

  const ModernHabitDetailsModal({
    super.key,
    required this.habit,
    required this.sections,
  });

  @override
  Widget build(BuildContext context) {
    debugPrint('[HABIT_DETAILS_MODAL] === BUILDING HABIT DETAILS MODAL ===');
    debugPrint('[HABIT_DETAILS_MODAL] Habit: ${habit.name}');
    debugPrint('[HABIT_DETAILS_MODAL] Sections available: ${sections.length}');
    
    final theme = Theme.of(context);
    final isDarkTheme = theme.brightness == Brightness.dark;
    debugPrint('[HABIT_DETAILS_MODAL] Theme: ${isDarkTheme ? 'Dark' : 'Light'}');
    
    // Calculate statistics
    final completionCount = habit.completions.values
        .where((completed) => completed)
        .length;
    final totalDays = habit.completions.length;
    final completionRate = totalDays > 0
        ? (completionCount / totalDays * 100).round()
        : 0;
    
    debugPrint('[HABIT_DETAILS_MODAL] Statistics calculated:');
    debugPrint('[HABIT_DETAILS_MODAL] - Completion count: $completionCount');
    debugPrint('[HABIT_DETAILS_MODAL] - Total days: $totalDays');
    debugPrint('[HABIT_DETAILS_MODAL] - Completion rate: $completionRate%');

    // Get section information
    final section = _getHabitSection();
    debugPrint('[HABIT_DETAILS_MODAL] Section found: ${section?.name ?? 'None'}');
    
    final sectionColor = section != null 
        ? SectionColorPalette.getColorFromHex(section.color, isDarkTheme: isDarkTheme)
        : theme.colorScheme.primary;
    debugPrint('[HABIT_DETAILS_MODAL] Section color: ${section?.color ?? 'Default primary'}');

    try {
      return Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: isDarkTheme 
                    ? Colors.black.withOpacity(0.3)
                    : Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header Section
              _buildHeader(theme, sectionColor, section),
              
              // Statistics Section
              _buildStatistics(theme, completionRate, completionCount),
              
              // Timeline Information
              _buildTimelineInfo(theme),
              
              // Action Area - FIXED: Pass context parameter
              _buildActionArea(theme, context),
            ],
          ),
        ),
      );
    } catch (e, stackTrace) {
      debugPrint('[ERROR] HABIT_DETAILS_MODAL: Failed to build modal - $e');
      debugPrint('[ERROR] HABIT_DETAILS_MODAL: StackTrace - $stackTrace');
      
      // Return a simple error dialog as fallback
      return Dialog(
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.error, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              const Text('Error loading habit details'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildHeader(ThemeData theme, Color sectionColor, Section? section) {
    debugPrint('[HABIT_DETAILS_MODAL] Building header section');
    debugPrint('[HABIT_DETAILS_MODAL] - Section color: $sectionColor');
    debugPrint('[HABIT_DETAILS_MODAL] - Section name: ${section?.name ?? 'No section'}');
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Habit name with section color indicator
          Row(
            children: [
              Container(
                width: 4,
                height: 24,
                decoration: BoxDecoration(
                  color: sectionColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  habit.name,
                  style: GoogleFonts.inter(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
          
          // Section information
          if (section != null) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.folder_outlined,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Text(
                  section.name,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatistics(ThemeData theme, int completionRate, int completionCount) {
    debugPrint('[HABIT_DETAILS_MODAL] Building statistics section');
    debugPrint('[HABIT_DETAILS_MODAL] - Completion rate: $completionRate%');
    debugPrint('[HABIT_DETAILS_MODAL] - Days completed: $completionCount');
    
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          // Completion Rate Card
          Expanded(
            child: _buildStatCard(
              theme: theme,
              icon: Icons.percent,
              value: '$completionRate%',
              label: 'Completion Rate',
              color: theme.brightness == Brightness.light
                  ? ModernTheme.lightSuccess
                  : ModernTheme.darkSuccess,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Days Completed Card
          Expanded(
            child: _buildStatCard(
              theme: theme,
              icon: Icons.check_circle_outline,
              value: '$completionCount',
              label: 'Days Completed',
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required ThemeData theme,
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 24,
            color: color,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.inter(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineInfo(ThemeData theme) {
    // For now, we'll show a placeholder since we don't have creation date in the current model
    // This can be enhanced when creation date is added to the Habit model
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today_outlined,
              size: 20,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Tracking Since',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _getTrackingStartDate(),
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionArea(ThemeData theme, BuildContext context) {
    debugPrint('[HABIT_DETAILS_MODAL] Building action area with theme: ${theme.brightness}');
    
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton(
            onPressed: () {
              debugPrint('[HABIT_DETAILS_MODAL] Close button pressed, popping modal');
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Close',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Section? _getHabitSection() {
    if (habit.sectionIds.isNotEmpty && sections.isNotEmpty) {
      return sections.firstWhere(
        (s) => s.id == habit.sectionIds.first,
        orElse: () => sections.first,
      );
    }
    return null;
  }

  String _getTrackingStartDate() {
    if (habit.completions.isEmpty) {
      return 'No data yet';
    }
    
    // Find the earliest date in completions
    final dates = habit.completions.keys.toList()..sort();
    if (dates.isNotEmpty) {
      final earliestDate = dates.first;
      return _formatDate(earliestDate);
    }
    
    return 'No data yet';
  }

  String _formatDate(DateTime date) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }
}