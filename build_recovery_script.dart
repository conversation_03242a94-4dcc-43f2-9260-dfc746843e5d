// Build Recovery and Debugging Script
// This script helps diagnose and fix build issues

import 'dart:io';

void main() async {
  print('=== BUILD RECOVERY AND DEBUGGING SCRIPT ===');
  
  try {
    // Step 1: Environment Check
    print('\n1. CHECKING BUILD ENVIRONMENT...');
    await checkBuildEnvironment();
    
    // Step 2: Clean Build Cache
    print('\n2. CLEANING BUILD CACHE...');
    await cleanBuildCache();
    
    // Step 3: Validate Dependencies
    print('\n3. VALIDATING DEPENDENCIES...');
    await validateDependencies();
    
    // Step 4: Test Build
    print('\n4. TESTING BUILD PROCESS...');
    await testBuild();
    
    print('\n=== BUILD RECOVERY COMPLETE ===');
    
  } catch (e, stackTrace) {
    print('\nERROR in build recovery: $e');
    print('Stack trace: $stackTrace');
  }
}

Future<void> checkBuildEnvironment() async {
  try {
    // Check Flutter
    final flutterResult = await Process.run('flutter', ['--version']);
    print('Flutter Status: ${flutterResult.exitCode == 0 ? "OK" : "ERROR"}');
    if (flutterResult.exitCode != 0) {
      print('Flutter Error: ${flutterResult.stderr}');
    }
    
    // Check Dart
    final dartResult = await Process.run('dart', ['--version']);
    print('Dart Status: ${dartResult.exitCode == 0 ? "OK" : "ERROR"}');
    
    // Check Android SDK (if available)
    final androidResult = await Process.run('flutter', ['doctor', '-v']);
    print('Android SDK Check completed');
    
  } catch (e) {
    print('Environment check failed: $e');
  }
}

Future<void> cleanBuildCache() async {
  try {
    print('Cleaning Flutter cache...');
    await Process.run('flutter', ['clean']);
    
    print('Cleaning pub cache...');
    await Process.run('flutter', ['pub', 'cache', 'clean']);
    
    print('Getting dependencies...');
    await Process.run('flutter', ['pub', 'get']);
    
    // Clean Android cache if directory exists
    final androidDir = Directory('android');
    if (await androidDir.exists()) {
      print('Cleaning Android cache...');
      if (Platform.isWindows) {
        await Process.run('cmd', ['/c', 'cd android && gradlew clean'], workingDirectory: '.');
      } else {
        await Process.run('bash', ['-c', 'cd android && ./gradlew clean'], workingDirectory: '.');
      }
    }
    
    print('Cache cleaning complete');
    
  } catch (e) {
    print('Cache cleaning failed: $e');
  }
}

Future<void> validateDependencies() async {
  try {
    print('Validating pubspec.yaml...');
    final pubspecFile = File('pubspec.yaml');
    if (await pubspecFile.exists()) {
      final content = await pubspecFile.readAsString();
      print('Pubspec exists and readable');
      
      // Check for shared_preferences
      if (content.contains('shared_preferences')) {
        print('✓ shared_preferences dependency found');
      } else {
        print('✗ shared_preferences dependency missing');
      }
      
      // Check for other critical dependencies
      final criticalDeps = ['flutter', 'provider', 'google_fonts'];
      for (final dep in criticalDeps) {
        if (content.contains(dep)) {
          print('✓ $dep dependency found');
        } else {
          print('✗ $dep dependency missing');
        }
      }
    } else {
      print('ERROR: pubspec.yaml not found');
    }
    
  } catch (e) {
    print('Dependency validation failed: $e');
  }
}

Future<void> testBuild() async {
  try {
    print('Testing build process...');
    
    // Test analysis
    print('Running flutter analyze...');
    final analyzeResult = await Process.run('flutter', ['analyze']);
    print('Analysis result: ${analyzeResult.exitCode == 0 ? "PASS" : "FAIL"}');
    if (analyzeResult.exitCode != 0) {
      print('Analysis errors: ${analyzeResult.stdout}');
    }
    
    // Test compilation (without running)
    print('Testing compilation...');
    final buildResult = await Process.run('flutter', ['build', 'apk', '--debug', '--no-shrink']);
    print('Build result: ${buildResult.exitCode == 0 ? "SUCCESS" : "FAILED"}');
    if (buildResult.exitCode != 0) {
      print('Build errors: ${buildResult.stderr}');
    }
    
  } catch (e) {
    print('Build test failed: $e');
  }
}