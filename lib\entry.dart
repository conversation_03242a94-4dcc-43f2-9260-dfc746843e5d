import 'package:flutter/foundation.dart';

/// Enum to define the type of habit entry
enum EntryType {
  boolean,
  numerical,
}

/// Enhanced Entry model that replaces the simple boolean completion system
/// Supports both boolean (yes/no) and numerical habits with notes
class Entry {
  final String id;
  final String habitId;
  final DateTime timestamp;
  final dynamic value; // bool for boolean habits, double for numerical habits
  final String? note;
  final EntryType type;

  Entry({
    String? id,
    required this.habitId,
    required this.timestamp,
    required this.value,
    this.note,
    required this.type,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();

  /// Get the boolean value for boolean entries
  bool get boolValue {
    if (type != EntryType.boolean) {
      throw StateError('Cannot get bool value from non-boolean entry');
    }
    return value as bool;
  }

  /// COMPREHENSIVE DEBUGGING: Add booleanValue getter for compatibility
  bool get booleanValue {
    debugPrint('[ENTRY] Getting booleanValue for entry ${id} (type: ${type.name})');
    if (type != EntryType.boolean) {
      debugPrint('[ENTRY] WARNING: Accessing booleanValue on non-boolean entry');
      throw StateError('Cannot get boolean value from non-boolean entry');
    }
    final result = value as bool;
    debugPrint('[ENTRY] booleanValue result: $result');
    return result;
  }

  /// Get the numerical value for numerical entries
  double? get numericalValue {
    debugPrint('[ENTRY] Getting numericalValue for entry ${id} (type: ${type.name})');
    if (type != EntryType.numerical) {
      debugPrint('[ENTRY] WARNING: Accessing numericalValue on non-numerical entry');
      return null;
    }
    final result = value as double?;
    debugPrint('[ENTRY] numericalValue result: $result');
    return result;
  }

  /// Check if this entry represents completion for the habit
  bool get isCompleted {
    debugPrint('[ENTRY] Checking isCompleted for entry ${id} (type: ${type.name})');
    switch (type) {
      case EntryType.boolean:
        final result = boolValue;
        debugPrint('[ENTRY] Boolean entry isCompleted: $result');
        return result;
      case EntryType.numerical:
        // For numerical habits, we need the target value to determine completion
        // This will be handled at the habit level
        final numValue = numericalValue;
        final result = numValue != null && numValue > 0;
        debugPrint('[ENTRY] Numerical entry isCompleted: $result (value: $numValue)');
        return result;
    }
  }

  /// Get the date (without time) for this entry
  DateTime get date {
    return DateTime(timestamp.year, timestamp.month, timestamp.day);
  }

  /// Serialization methods for database persistence
  Map<String, dynamic> toJson() => {
    'id': id,
    'habitId': habitId,
    'timestamp': timestamp.toIso8601String(),
    'value': value,
    'note': note,
    'type': type.name,
  };

  static Entry fromJson(Map<String, dynamic> json) {
    final typeString = json['type'] as String;
    final entryType = EntryType.values.firstWhere(
      (e) => e.name == typeString,
      orElse: () => EntryType.boolean,
    );

    return Entry(
      id: json['id'],
      habitId: json['habitId'],
      timestamp: DateTime.parse(json['timestamp']),
      value: json['value'],
      note: json['note'],
      type: entryType,
    );
  }

  /// Create a copy of this entry with updated properties
  Entry copyWith({
    String? habitId,
    DateTime? timestamp,
    dynamic value,
    String? note,
    EntryType? type,
  }) {
    return Entry(
      id: id,
      habitId: habitId ?? this.habitId,
      timestamp: timestamp ?? this.timestamp,
      value: value ?? this.value,
      note: note ?? this.note,
      type: type ?? this.type,
    );
  }

  @override
  String toString() {
    return 'Entry(id: $id, habitId: $habitId, timestamp: $timestamp, value: $value, note: $note, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Entry &&
        other.id == id &&
        other.habitId == habitId &&
        other.timestamp == timestamp &&
        other.value == value &&
        other.note == note &&
        other.type == type;
  }

  @override
  int get hashCode {
    return Object.hash(id, habitId, timestamp, value, note, type);
  }
}