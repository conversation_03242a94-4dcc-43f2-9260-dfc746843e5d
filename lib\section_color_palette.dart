import 'package:flutter/material.dart';

class SectionColorPalette {
  // Predefined color palette for sections
  static const List<SectionColor> lightThemeColors = [
    SectionColor(name: 'Blue', hex: '#3B82F6', color: Color(0xFF3B82F6)),
    SectionColor(name: 'Green', hex: '#10B981', color: Color(0xFF10B981)),
    SectionColor(name: 'Purple', hex: '#8B5CF6', color: Color(0xFF8B5CF6)),
    SectionColor(name: 'Orange', hex: '#F97316', color: Color(0xFFF97316)),
    SectionColor(name: 'Pink', hex: '#EC4899', color: Color(0xFFEC4899)),
    SectionColor(name: 'Teal', hex: '#06B6D4', color: Color(0xFF06B6D4)),
    SectionColor(name: 'Red', hex: '#EF4444', color: Color(0xFFEF4444)),
    SectionColor(name: 'Indigo', hex: '#6366F1', color: Color(0xFF6366F1)),
    SectionColor(name: 'Yellow', hex: '#EAB308', color: Color(0xFFEAB308)),
    SectionColor(name: 'Emerald', hex: '#059669', color: Color(0xFF059669)),
    SectionColor(name: 'Rose', hex: '#F43F5E', color: Color(0xFFF43F5E)),
    SectionColor(name: 'Cyan', hex: '#0891B2', color: Color(0xFF0891B2)),
  ];

  static const List<SectionColor> darkThemeColors = [
    SectionColor(name: 'Blue', hex: '#60A5FA', color: Color(0xFF60A5FA)),
    SectionColor(name: 'Green', hex: '#34D399', color: Color(0xFF34D399)),
    SectionColor(name: 'Purple', hex: '#A78BFA', color: Color(0xFFA78BFA)),
    SectionColor(name: 'Orange', hex: '#FB923C', color: Color(0xFFFB923C)),
    SectionColor(name: 'Pink', hex: '#F472B6', color: Color(0xFFF472B6)),
    SectionColor(name: 'Teal', hex: '#22D3EE', color: Color(0xFF22D3EE)),
    SectionColor(name: 'Red', hex: '#F87171', color: Color(0xFFF87171)),
    SectionColor(name: 'Indigo', hex: '#818CF8', color: Color(0xFF818CF8)),
    SectionColor(name: 'Yellow', hex: '#FBBF24', color: Color(0xFFFBBF24)),
    SectionColor(name: 'Emerald', hex: '#10B981', color: Color(0xFF10B981)),
    SectionColor(name: 'Rose', hex: '#FB7185', color: Color(0xFFFB7185)),
    SectionColor(name: 'Cyan', hex: '#06B6D4', color: Color(0xFF06B6D4)),
  ];

  // Get theme-appropriate color palette
  static List<SectionColor> getColorsForTheme(bool isDarkTheme) {
    return isDarkTheme ? darkThemeColors : lightThemeColors;
  }

  // Get Color object from hex string
  static Color getColorFromHex(String hex, {required bool isDarkTheme}) {
    final colors = getColorsForTheme(isDarkTheme);
    
    // Find matching color in current theme
    final matchingColor = colors.firstWhere(
      (color) => color.hex == hex,
      orElse: () => colors.first, // Default to first color if not found
    );
    
    return matchingColor.color;
  }

  // Get next available color for new section
  static String getNextAvailableColor(List<String> usedColors, {required bool isDarkTheme}) {
    final availableColors = getColorsForTheme(isDarkTheme);
    
    for (final color in availableColors) {
      if (!usedColors.contains(color.hex)) {
        return color.hex;
      }
    }
    
    // If all colors are used, return first color
    return availableColors.first.hex;
  }

  // Get similar alternative colors when user selects already-used color
  static List<String> getSimilarColors(String selectedHex, List<String> usedColors, {required bool isDarkTheme}) {
    final availableColors = getColorsForTheme(isDarkTheme);
    final unusedColors = availableColors
        .where((color) => !usedColors.contains(color.hex))
        .map((color) => color.hex)
        .toList();
    
    // Return up to 3 unused colors as alternatives
    return unusedColors.take(3).toList();
  }

  // Validate if hex string is a valid color
  static bool isValidHex(String hex) {
    return RegExp(r'^#[0-9A-Fa-f]{6}$').hasMatch(hex);
  }

  // Convert hex string to Color object
  static Color hexToColor(String hex) {
    if (!isValidHex(hex)) {
      return const Color(0xFF3B82F6); // Default blue
    }
    
    return Color(int.parse(hex.substring(1, 7), radix: 16) + 0xFF000000);
  }

  // Convert Color object to hex string
  static String colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }
}

class SectionColor {
  final String name;
  final String hex;
  final Color color;

  const SectionColor({
    required this.name,
    required this.hex,
    required this.color,
  });
}