<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phase 1 Analytics UI Mockup</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .material-icons { vertical-align: middle; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="max-w-6xl mx-auto p-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">Phase 1: Enhanced Analytics Foundation</h1>
        <p class="text-gray-600 mb-8">UI/UX mockups showing the new entry model and score system integration</p>

        <!-- Section 1: Enhanced Habit List with Scores -->
        <section class="mb-12">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">1. Enhanced Habit List with Score & Streak</h2>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <!-- Header -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-800">All Habits</h3>
                        <button class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 flex items-center gap-2">
                            <span class="material-icons text-sm">add</span>
                            Add Habit
                        </button>
                    </div>
                </div>
                
                <!-- Habit Items -->
                <div class="divide-y divide-gray-200">
                    <!-- Boolean Habit with Score -->
                    <div class="px-6 py-4 hover:bg-gray-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-1 h-12 bg-indigo-600 rounded-full"></div>
                                <div>
                                    <h4 class="font-medium text-gray-800">Morning Exercise</h4>
                                    <div class="flex items-center gap-4 mt-1">
                                        <span class="text-sm text-gray-500">Daily</span>
                                        <span class="text-sm text-indigo-600 font-medium">Score: 85%</span>
                                        <span class="text-sm text-green-600 font-medium">🔥 15 day streak</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <button class="w-10 h-10 rounded-md border-2 border-green-500 bg-green-500 flex items-center justify-center">
                                    <span class="material-icons text-white text-lg">check</span>
                                </button>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <span class="material-icons">more_vert</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Numerical Habit with Score -->
                    <div class="px-6 py-4 hover:bg-gray-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-1 h-12 bg-purple-600 rounded-full"></div>
                                <div>
                                    <h4 class="font-medium text-gray-800">Water Intake</h4>
                                    <div class="flex items-center gap-4 mt-1">
                                        <span class="text-sm text-gray-500">Target: 8 glasses</span>
                                        <span class="text-sm text-indigo-600 font-medium">Score: 72%</span>
                                        <span class="text-sm text-orange-600 font-medium">🔥 3 day streak</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="flex items-center gap-1 bg-gray-100 rounded-md px-2 py-1">
                                    <input type="number" value="6" min="0" max="20" class="w-12 text-center bg-transparent outline-none font-medium">
                                    <span class="text-sm text-gray-600">/ 8</span>
                                </div>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <span class="material-icons">more_vert</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Habit with Note -->
                    <div class="px-6 py-4 hover:bg-gray-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-1 h-12 bg-green-600 rounded-full"></div>
                                <div>
                                    <h4 class="font-medium text-gray-800">Reading</h4>
                                    <div class="flex items-center gap-4 mt-1">
                                        <span class="text-sm text-gray-500">Daily</span>
                                        <span class="text-sm text-indigo-600 font-medium">Score: 92%</span>
                                        <span class="text-sm text-gray-500 italic">📝 "Finished chapter 3"</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <button class="w-10 h-10 rounded-md border-2 border-green-500 bg-green-500 flex items-center justify-center">
                                    <span class="material-icons text-white text-lg">check</span>
                                </button>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <span class="material-icons">more_vert</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2: Entry Dialog for Numerical Habits -->
        <section class="mb-12">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">2. Enhanced Entry Dialog (Numerical Habits)</h2>
            <div class="bg-white rounded-lg shadow-lg border border-gray-200 max-w-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">Water Intake - Today</h3>
                </div>
                <div class="p-6">
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">How many glasses?</label>
                        <div class="flex items-center gap-4">
                            <button class="w-10 h-10 rounded-md bg-gray-100 hover:bg-gray-200 flex items-center justify-center">
                                <span class="material-icons">remove</span>
                            </button>
                            <input type="number" value="6" class="w-20 text-center text-2xl font-semibold border-2 border-gray-300 rounded-md">
                            <button class="w-10 h-10 rounded-md bg-gray-100 hover:bg-gray-200 flex items-center justify-center">
                                <span class="material-icons">add</span>
                            </button>
                        </div>
                        <p class="text-sm text-gray-500 mt-2">Target: 8 glasses per day</p>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Add a note (optional)</label>
                        <textarea 
                            placeholder="e.g., Drank more during workout..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            rows="3"
                        ></textarea>
                    </div>
                    
                    <div class="flex gap-3">
                        <button class="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                            Save Entry
                        </button>
                        <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 3: Score Calculation Visualization -->
        <section class="mb-12">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">3. Score System Visualization</h2>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="font-semibold text-gray-800 mb-4">How Habit Score Works</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-gray-700 mb-2">Score Factors</h4>
                        <div class="space-y-3">
                            <div class="flex items-center gap-3">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <span class="material-icons text-green-600">trending_up</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">Consistency</p>
                                    <p class="text-sm text-gray-600">Regular completions increase score</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <span class="material-icons text-orange-600">schedule</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">Recency</p>
                                    <p class="text-sm text-gray-600">Recent activity weighted higher</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <span class="material-icons text-red-600">trending_down</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">Decay</p>
                                    <p class="text-sm text-gray-600">Score decreases with missed days</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-700 mb-2">Score Ranges</h4>
                        <div class="space-y-2">
                            <div class="flex items-center gap-3">
                                <div class="w-full bg-gray-200 rounded-full h-6 relative">
                                    <div class="bg-green-500 h-6 rounded-full" style="width: 90%"></div>
                                    <span class="absolute inset-0 flex items-center justify-center text-xs font-medium">90-100%</span>
                                </div>
                                <span class="text-sm text-gray-600 w-20">Excellent</span>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-full bg-gray-200 rounded-full h-6 relative">
                                    <div class="bg-blue-500 h-6 rounded-full" style="width: 70%"></div>
                                    <span class="absolute inset-0 flex items-center justify-center text-xs font-medium">70-89%</span>
                                </div>
                                <span class="text-sm text-gray-600 w-20">Good</span>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-full bg-gray-200 rounded-full h-6 relative">
                                    <div class="bg-yellow-500 h-6 rounded-full" style="width: 50%"></div>
                                    <span class="absolute inset-0 flex items-center justify-center text-xs font-medium">50-69%</span>
                                </div>
                                <span class="text-sm text-gray-600 w-20">Fair</span>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-full bg-gray-200 rounded-full h-6 relative">
                                    <div class="bg-red-500 h-6 rounded-full" style="width: 30%"></div>
                                    <span class="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">0-49%</span>
                                </div>
                                <span class="text-sm text-gray-600 w-20">Needs Work</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 4: Create Habit Dialog Enhancement -->
        <section class="mb-12">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">4. Enhanced Create Habit Dialog</h2>
            <div class="bg-white rounded-lg shadow-lg border border-gray-200 max-w-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">Create New Habit</h3>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Habit Name</label>
                        <input type="text" placeholder="e.g., Drink Water" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Habit Type</label>
                        <div class="grid grid-cols-2 gap-3">
                            <button class="px-4 py-3 border-2 border-gray-300 rounded-md hover:border-indigo-500 focus:border-indigo-500 focus:bg-indigo-50">
                                <span class="material-icons text-gray-600">check_circle</span>
                                <p class="text-sm font-medium mt-1">Yes/No</p>
                                <p class="text-xs text-gray-500">Simple completion</p>
                            </button>
                            <button class="px-4 py-3 border-2 border-indigo-500 bg-indigo-50 rounded-md">
                                <span class="material-icons text-indigo-600">numbers</span>
                                <p class="text-sm font-medium mt-1">Numerical</p>
                                <p class="text-xs text-gray-500">Track amounts</p>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-6" id="numerical-fields">
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Target Value</label>
                                <input type="number" placeholder="8" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Unit</label>
                                <input type="text" placeholder="glasses" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500">
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Target Type</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500">
                                <option>At least (minimum)</option>
                                <option>At most (maximum)</option>
                                <option>Exactly</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="flex gap-3">
                        <button class="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                            Create Habit
                        </button>
                        <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>