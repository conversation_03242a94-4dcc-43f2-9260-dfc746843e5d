// lib/theme_notifier.dart
import 'package:flutter/material.dart';
import 'settings_service.dart';

class ThemeNotifier extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  final SettingsService _settingsService = SettingsService.instance;
  bool _isInitialized = false;

  ThemeMode get themeMode => _themeMode;

  /// Initialize theme from saved preferences
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      final isDarkMode = await _settingsService.loadTheme();
      _themeMode = isDarkMode ? ThemeMode.dark : ThemeMode.light;
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('[THEME_NOTIFIER] Error loading theme: $e');
      _themeMode = ThemeMode.system;
      _isInitialized = true;
    }
  }

  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    _saveThemeMode();
    notifyListeners();
  }

  void toggleTheme() {
    if (_themeMode == ThemeMode.light) {
      _themeMode = ThemeMode.dark;
    } else {
      _themeMode = ThemeMode.light;
    }
    _saveThemeMode();
    notifyListeners();
  }

  Future<void> _saveThemeMode() async {
    try {
      final isDarkMode = _themeMode == ThemeMode.dark;
      await _settingsService.saveTheme(isDarkMode);
    } catch (e) {
      debugPrint('[THEME_NOTIFIER] Error saving theme: $e');
    }
  }
}