import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'habit.dart';
import 'section.dart';
import 'database_service.dart';
import 'manage_sections_screen.dart';
import 'theme_notifier.dart';
import 'habit_table_view.dart';

class HabitsScreen extends StatefulWidget {
  const HabitsScreen({super.key});

  @override
  State<HabitsScreen> createState() => _HabitsScreenState();
}

class _HabitsScreenState extends State<HabitsScreen> {
  final _databaseService = DatabaseService();

  // State variables
  late Future<void> _dataFuture;
  List<Habit> _allHabits = [];
  List<Section> _allSections = [];
  String? _selectedSectionId;
  List<Habit> _displayedHabits = [];

  @override
  void initState() {
    super.initState();
    _dataFuture = _loadAllData();
  }

  Future<void> _loadAllData() async {
    try {
      final results = await Future.wait([
        _databaseService.loadAllSections(),
        _databaseService.loadAllHabits(),
      ]);

      _allSections = results[0] as List<Section>;
      _allHabits = results[1] as List<Habit>;

      _filterHabits();
    } catch (e, stackTrace) {
      debugPrint('ERROR: Failed to load data - $e');
      debugPrint('StackTrace: $stackTrace');
      rethrow;
    }
  }

  void _filterHabits() {
    if (_selectedSectionId == null) {
      _displayedHabits = List.from(_allHabits);
    } else {
      _displayedHabits = _allHabits
          .where((habit) => habit.sectionIds.contains(_selectedSectionId))
          .toList();
    }
  }

  // CRITICAL FIX: Add dedicated reload method
  Future<void> _reloadData() async {
    setState(() {
      _dataFuture = _loadAllData();
    });
  }

  Future<void> _onReorder(int oldIndex, int newIndex) async {
    try {
      debugPrint('[HABITS_SCREEN] Reordering habit from $oldIndex to $newIndex');
      
      setState(() {
        // Adjust newIndex if moving item down the list
        if (oldIndex < newIndex) {
          newIndex -= 1;
        }
        
        // Update local list
        final habit = _displayedHabits.removeAt(oldIndex);
        _displayedHabits.insert(newIndex, habit);
        
        // Update the master habits order
        _updateMasterHabitsOrder();
      });

      // Save the new order to the database
      await _databaseService.saveAllHabits(_allHabits);
      
      // THIS IS THE CRITICAL FIX: Reload all data to reflect the new order
      await _reloadData();
      
      debugPrint('[HABITS_SCREEN] Successfully updated habit order');
      
    } catch (e, stackTrace) {
      debugPrint('[ERROR] Failed to reorder habits: $e');
      debugPrint('[ERROR] StackTrace: $stackTrace');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reorder habits: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      
      // Reload data to revert changes
      await _reloadData();
    }
  }

  void _updateMasterHabitsOrder() {
    final List<Habit> newOrderedHabits = [];

    // Add displayed habits in their new order
    for (final displayedHabit in _displayedHabits) {
      final masterHabitIndex = _allHabits.indexWhere(
        (h) => h.id == displayedHabit.id,
      );
      if (masterHabitIndex != -1) {
        newOrderedHabits.add(_allHabits[masterHabitIndex]);
      }
    }

    // Add any habits not in displayed list (from other sections)
    for (final masterHabit in _allHabits) {
      if (!_displayedHabits.any((h) => h.id == masterHabit.id)) {
        newOrderedHabits.add(masterHabit);
      }
    }

    // Update the master list
    _allHabits.clear();
    _allHabits.addAll(newOrderedHabits);
  }

  // Add Habit Dialog
  Future<void> _showAddHabitDialog() async {
    // Guard clause: Ensure sections are loaded
    if (_allSections.isEmpty) {
      final defaultSection = Section(name: 'My Habits');
      _allSections.add(defaultSection);
      await _databaseService.addSection(defaultSection);
    }

    final TextEditingController nameController = TextEditingController();
    String selectedSectionId = _allSections.first.id;

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Add New Habit'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Habit Name',
                  hintText: 'Enter habit name',
                ),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedSectionId,
                decoration: const InputDecoration(labelText: 'Section'),
                items: _allSections.map((section) {
                  return DropdownMenuItem<String>(
                    value: section.id,
                    child: Text(section.name),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    selectedSectionId = value;
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final habitName = nameController.text.trim();
                if (habitName.isNotEmpty) {
                  try {
                    final newHabit = Habit(
                      name: habitName,
                      sectionIds: [selectedSectionId],
                    );

                    await _databaseService.addHabit(newHabit);

                    // Reload data to reflect changes
                    await _reloadData();

                    if (mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Added habit: $habitName'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Failed to add habit: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        title: DropdownButton<String?>(
          value: _selectedSectionId,
          isExpanded: true,
          underline: Container(), // Removes the default underline
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).appBarTheme.foregroundColor,
          ),
          dropdownColor: Theme.of(context).colorScheme.surface,
          onChanged: (newValue) {
            _onSectionSelected(newValue);
          },
          items: [
            // The "All" option
            const DropdownMenuItem(
              value: null, // Represents the "All" filter
              child: Text("All Habits"),
            ),
            // Map over sections to create the rest of the items
            ..._allSections.map<DropdownMenuItem<String>>((Section section) {
              return DropdownMenuItem<String>(
                value: section.id,
                child: Text(section.name),
              );
            }).toList(),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(
              Theme.of(context).brightness == Brightness.dark
                  ? Icons.light_mode
                  : Icons.dark_mode,
            ),
            onPressed: () {
              final notifier = Provider.of<ThemeNotifier>(
                context,
                listen: false,
              );
              notifier.toggleTheme();
            },
            tooltip: Theme.of(context).brightness == Brightness.dark
                ? 'Switch to light mode'
                : 'Switch to dark mode',
            color: Theme.of(context).appBarTheme.iconTheme?.color,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showManageSectionsScreen,
            tooltip: 'Manage sections',
            color: Theme.of(context).appBarTheme.iconTheme?.color,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddHabitDialog,
            tooltip: 'Add new habit',
            color: Theme.of(context).appBarTheme.iconTheme?.color,
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Main content
            Expanded(
              child: FutureBuilder<void>(
                future: _dataFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.red,
                          ),
                          const SizedBox(height: 16),
                          Text('Error loading data: ${snapshot.error}'),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _dataFuture = _loadAllData();
                              });
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  } else if (_displayedHabits.isEmpty) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.library_add_check_outlined,
                            size: 60,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No habits yet',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Tap the + button to add your first habit',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    );
                  }

                  // Build the TableView with habits
                  final dates = _generateDates();

                  return HabitTableView(
                    habits: _displayedHabits,
                    dates: dates,
                    sections: _allSections,
                    onReorder: _onReorder,
                    showReorderDialog: true,
                    onDataChanged: _reloadData, // THIS IS THE CRITICAL FIX
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<DateTime> _generateDates() {
    final now = DateTime.now();
    final List<DateTime> dateList = [];
    for (int i = 29; i >= 0; i--) {
      dateList.add(DateTime(now.year, now.month, now.day - i));
    }
    return dateList.reversed.toList();
  }

  // INTERACTIVE FEATURE: Show manage sections screen
  Future<void> _showManageSectionsScreen() async {
    // Wait for the manage sections screen to be closed
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            ManageSectionsScreen(initialSections: _allSections),
      ),
    );

    // THIS IS THE CRITICAL FIX: Always reload data after returning
    await _reloadData();
  }

  // DROPDOWN FILTERING: Handle section selection from dropdown
  void _onSectionSelected(String? sectionId) {
    setState(() {
      _selectedSectionId = sectionId;
      _filterHabits();
    });
  }
}