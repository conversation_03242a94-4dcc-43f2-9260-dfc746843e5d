import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;

/// Widget to display habit score as a circular progress indicator
class CircularScoreWidget extends StatelessWidget {
  final double score; // 0-100
  final double size;
  final Color? color;

  const CircularScoreWidget({
    super.key,
    required this.score,
    this.size = 64,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = color ?? theme.colorScheme.primary;
    final backgroundColor = theme.colorScheme.outline.withOpacity(0.2);
    
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        children: [
          // Background circle
          CustomPaint(
            size: Size(size, size),
            painter: _CircularProgressPainter(
              progress: 1.0,
              color: backgroundColor,
              strokeWidth: size * 0.08,
            ),
          ),
          // Progress circle
          CustomPaint(
            size: Size(size, size),
            painter: _CircularProgressPainter(
              progress: score / 100,
              color: progressColor,
              strokeWidth: size * 0.08,
            ),
          ),
          // Score text
          Center(
            child: Text(
              '${score.round()}%',
              style: GoogleFonts.inter(
                fontSize: size * 0.2,
                fontWeight: FontWeight.w700,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Custom painter for circular progress
class _CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color color;
  final double strokeWidth;

  _CircularProgressPainter({
    required this.progress,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Draw arc
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2, // Start from top
      2 * math.pi * progress,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Widget to display habit score as a linear progress bar
class LinearScoreWidget extends StatelessWidget {
  final String habitName;
  final double score; // 0-100
  final Color? color;

  const LinearScoreWidget({
    super.key,
    required this.habitName,
    required this.score,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = color ?? theme.colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                habitName,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurface,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              '${score.round()}%',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        LinearProgressIndicator(
          value: score / 100,
          backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(progressColor),
          minHeight: 6,
        ),
      ],
    );
  }
}

/// Widget to display streak information
class StreakDisplayWidget extends StatelessWidget {
  final int currentStreak;
  final int bestStreak;
  final int totalCompletions;

  const StreakDisplayWidget({
    super.key,
    required this.currentStreak,
    required this.bestStreak,
    required this.totalCompletions,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Streak Stats',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          _buildStreakRow(
            context,
            icon: '🔥',
            label: 'Current',
            value: '$currentStreak days',
            valueColor: _getStreakColor(currentStreak),
          ),
          const SizedBox(height: 12),
          _buildStreakRow(
            context,
            icon: '⭐',
            label: 'Best',
            value: '$bestStreak days',
            valueColor: Colors.amber,
          ),
          const SizedBox(height: 12),
          _buildStreakRow(
            context,
            icon: '📊',
            label: 'Total',
            value: '$totalCompletions days',
            valueColor: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ],
      ),
    );
  }

  Widget _buildStreakRow(
    BuildContext context, {
    required String icon,
    required String label,
    required String value,
    required Color valueColor,
  }) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Text(
          icon,
          style: const TextStyle(fontSize: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 14,
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ),
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: valueColor,
          ),
        ),
      ],
    );
  }

  Color _getStreakColor(int streak) {
    if (streak >= 30) return Colors.purple;
    if (streak >= 14) return Colors.orange;
    if (streak >= 7) return Colors.green;
    if (streak >= 3) return Colors.blue;
    return Colors.grey;
  }
}

/// Compact widget to display score and streak inline
class CompactScoreStreakWidget extends StatelessWidget {
  final double score;
  final int streak;
  final Color? scoreColor;

  const CompactScoreStreakWidget({
    super.key,
    required this.score,
    required this.streak,
    this.scoreColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        // Score
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: (scoreColor ?? theme.colorScheme.primary).withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            'Score: ${score.round()}%',
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: scoreColor ?? theme.colorScheme.primary,
            ),
          ),
        ),
        const SizedBox(width: 8),
        // Streak
        if (streak > 0) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getStreakColor(streak).withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('🔥', style: TextStyle(fontSize: 10)),
                const SizedBox(width: 2),
                Text(
                  '$streak day${streak == 1 ? '' : 's'}',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: _getStreakColor(streak),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Color _getStreakColor(int streak) {
    if (streak >= 30) return Colors.purple;
    if (streak >= 14) return Colors.orange;
    if (streak >= 7) return Colors.green;
    if (streak >= 3) return Colors.blue;
    return Colors.grey;
  }
}

/// Widget to display numerical habit progress
class NumericalProgressWidget extends StatelessWidget {
  final double currentValue;
  final double targetValue;
  final String? unit;

  const NumericalProgressWidget({
    super.key,
    required this.currentValue,
    required this.targetValue,
    this.unit,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progress = targetValue > 0 ? (currentValue / targetValue).clamp(0.0, 1.0) : 0.0;
    final percentage = (progress * 100).round();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${currentValue.toStringAsFixed(currentValue % 1 == 0 ? 0 : 1)}',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          Text(
            ' / ${targetValue.toStringAsFixed(targetValue % 1 == 0 ? 0 : 1)}',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          if (unit != null) ...[
            const SizedBox(width: 4),
            Text(
              unit!,
              style: GoogleFonts.inter(
                fontSize: 12,
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
            ),
          ],
          const SizedBox(width: 8),
          Text(
            '($percentage%)',
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: _getProgressColor(progress),
            ),
          ),
        ],
      ),
    );
  }

  Color _getProgressColor(double progress) {
    if (progress >= 1.0) return Colors.green;
    if (progress >= 0.8) return Colors.orange;
    if (progress >= 0.5) return Colors.blue;
    return Colors.grey;
  }
}