import 'package:flutter/material.dart';
import 'modern_theme.dart';

enum HabitStatus { completed, missed, pending }

class StatusIndicator extends StatefulWidget {
  final HabitStatus status;
  final bool isToday;
  final VoidCallback? onTap;

  const StatusIndicator({
    super.key,
    required this.status,
    this.isToday = false,
    this.onTap,
  });

  @override
  State<StatusIndicator> createState() => _StatusIndicatorState();
}

class _StatusIndicatorState extends State<StatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final successColor = theme.brightness == Brightness.light
        ? ModernTheme.lightSuccess
        : ModernTheme.darkSuccess;

    try {
      Widget indicator;
      
      switch (widget.status) {
        case HabitStatus.completed:
          final isDark = theme.brightness == Brightness.dark;
          final size = isDark ? ModernTheme.completionIndicatorSizeDark : ModernTheme.completionIndicatorSize;
          final accentColor = isDark ? ModernTheme.darkAccent : ModernTheme.lightAccent;
          
          indicator = AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: size,
            height: size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: accentColor,
              boxShadow: isDark ? [
                BoxShadow(
                  color: accentColor.withOpacity(0.3),
                  blurRadius: 4,
                  spreadRadius: 1,
                ),
              ] : null,
            ),
            child: Icon(
              Icons.circle, // Filled circle as per design system
              size: size * 0.8,
              color: accentColor,
            ),
          );
          break;
        case HabitStatus.missed:
          indicator = Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.1),
              border: Border.all(
                color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                width: 2,
              ),
            ),
            child: Icon(
              Icons.close,
              size: 20,
              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
            ),
          );
          break;
        case HabitStatus.pending:
          final isDark = theme.brightness == Brightness.dark;
          final size = isDark ? ModernTheme.completionIndicatorSizeDark : ModernTheme.completionIndicatorSize;
          final inactiveColor = isDark ? ModernTheme.darkTextSecondary : ModernTheme.lightTextSecondary;
          final accentColor = isDark ? ModernTheme.darkAccent : ModernTheme.lightAccent;
          
          indicator = Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.transparent,
              border: Border.all(
                color: widget.isToday ? accentColor : inactiveColor,
                width: 1.5, // Refined 1.5px stroke weight
              ),
            ),
            child: Icon(
              Icons.circle_outlined, // Empty circle as per design system
              size: size * 0.7,
              color: widget.isToday ? accentColor : inactiveColor,
            ),
          );
          break;
      }

      if (widget.onTap != null) {
        return GestureDetector(
          onTapDown: (_) => _animationController.forward(),
          onTapUp: (_) {
            _animationController.reverse();
            widget.onTap?.call();
          },
          onTapCancel: () => _animationController.reverse(),
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: indicator,
              );
            },
          ),
        );
      }

      return indicator;
    } catch (e, stackTrace) {
      debugPrint('ERROR in StatusIndicator: $e');
      debugPrint('StackTrace: $stackTrace');
      return Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.red.withOpacity(0.3),
        ),
        child: const Center(
          child: Text('!', style: TextStyle(fontSize: 14, color: Colors.white)),
        ),
      );
    }
  }
}
