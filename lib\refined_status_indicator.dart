import 'package:flutter/material.dart';
import 'modern_theme.dart';

class RefinedStatusIndicator extends StatefulWidget {
  final bool isCompleted;
  final VoidCallback? onTap;
  final double? size;
  final Color? activeColor;
  final Color? inactiveColor;

  const RefinedStatusIndicator({
    super.key,
    required this.isCompleted,
    this.onTap,
    this.size,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  State<RefinedStatusIndicator> createState() => _RefinedStatusIndicatorState();
}

class _RefinedStatusIndicatorState extends State<RefinedStatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.9,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(RefinedStatusIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isCompleted != widget.isCompleted) {
      if (widget.isCompleted) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Use design system sizes
    final effectiveSize = widget.size ?? 
        (isDark ? ModernTheme.completionIndicatorSizeDark : ModernTheme.completionIndicatorSize);
    
    final effectiveActiveColor = widget.activeColor ?? 
        (isDark ? ModernTheme.darkAccent : ModernTheme.lightAccent);
    final effectiveInactiveColor = widget.inactiveColor ?? 
        (isDark ? ModernTheme.darkTextSecondary : ModernTheme.lightTextSecondary);

    // Initialize color animation based on current theme
    _colorAnimation = ColorTween(
      begin: effectiveInactiveColor,
      end: effectiveActiveColor,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Set initial animation state
    if (widget.isCompleted && _animationController.value == 0) {
      _animationController.value = 1.0;
    } else if (!widget.isCompleted && _animationController.value == 1) {
      _animationController.value = 0.0;
    }

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: ModernTheme.minTouchTarget,
        height: ModernTheme.minTouchTarget,
        alignment: Alignment.center,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                width: effectiveSize,
                height: effectiveSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: widget.isCompleted ? effectiveActiveColor : Colors.transparent,
                  border: Border.all(
                    color: _colorAnimation.value ?? effectiveInactiveColor,
                    width: 1.5, // Refined 1.5px stroke weight
                  ),
                  boxShadow: isDark && widget.isCompleted ? [
                    BoxShadow(
                      color: effectiveActiveColor.withOpacity(0.3),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ] : null,
                ),
                child: widget.isCompleted
                    ? Icon(
                        Icons.circle,
                        size: effectiveSize * 0.8,
                        color: effectiveActiveColor,
                      )
                    : null,
              ),
            );
          },
        ),
      ),
    );
  }
}