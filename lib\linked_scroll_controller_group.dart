// lib/linked_scroll_controller_group.dart
import 'package:flutter/widgets.dart';

class LinkedScrollControllerGroup {
  final List<ScrollController> _controllers = [];
  ScrollController? _masterController;

  // When a new controller is created, it's added to the group.
  ScrollController addAndReturn() {
    final controller = ScrollController();
    _controllers.add(controller);
    controller.addListener(() => _onScroll(controller));
    return controller;
  }

  // The listener that does the magic.
  void _onScroll(ScrollController currentController) {
    // If the current scroll event is not from the master controller, ignore it.
    // This prevents chaotic feedback loops.
    if (_masterController != null && _masterController != currentController) return;

    // Set the current scroller as the master.
    _masterController = currentController;
    final offset = currentController.offset;

    // For every other controller in the group, jump to the same offset.
    for (var c in _controllers) {
      if (c != currentController && c.hasClients) {
        c.jumpTo(offset);
      }
    }

    // After a short delay, release the master lock.
    Future.delayed(const Duration(milliseconds: 100)).then((_) {
      _masterController = null;
    });
  }

  // Public getter to access controllers safely
  List<ScrollController> get controllers => List.unmodifiable(_controllers);

  void dispose() {
    for (var c in _controllers) {
      c.dispose();
    }
  }
}