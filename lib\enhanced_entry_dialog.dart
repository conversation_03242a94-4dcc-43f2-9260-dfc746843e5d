import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'habit.dart';
import 'entry.dart';
import 'database_service.dart';
import 'modern_theme.dart';

/// Enhanced entry dialog that supports both boolean and numerical habits with notes
class EnhancedEntryDialog extends StatefulWidget {
  final Habit habit;
  final DateTime date;
  final Entry? existingEntry;

  const EnhancedEntryDialog({
    super.key,
    required this.habit,
    required this.date,
    this.existingEntry,
  });

  @override
  State<EnhancedEntryDialog> createState() => _EnhancedEntryDialogState();
}

class _EnhancedEntryDialogState extends State<EnhancedEntryDialog> {
  final _databaseService = DatabaseService();
  final _noteController = TextEditingController();
  final _numericalController = TextEditingController();
  
  bool _booleanValue = false;
  double _numericalValue = 0.0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeValues();
  }

  void _initializeValues() {
    if (widget.existingEntry != null) {
      final entry = widget.existingEntry!;
      _noteController.text = entry.note ?? '';
      
      if (entry.type == EntryType.boolean) {
        _booleanValue = entry.boolValue;
        debugPrint('[ENHANCED_ENTRY_DIALOG] Initialized boolean value: $_booleanValue');
      } else if (entry.type == EntryType.numerical) {
        final numValue = entry.numericalValue;
        _numericalValue = numValue ?? 0.0;
        _numericalController.text = _numericalValue.toString();
        debugPrint('[ENHANCED_ENTRY_DIALOG] Initialized numerical value: $_numericalValue (original: $numValue)');
      }
    } else {
      // Initialize with current values if they exist
      if (widget.habit.type == HabitType.boolean) {
        _booleanValue = widget.habit.isCompletedOnDate(widget.date);
      } else {
        _numericalValue = widget.habit.getNumericalValueForDate(widget.date) ?? 0.0;
        _numericalController.text = _numericalValue.toString();
      }
    }
  }

  @override
  void dispose() {
    _noteController.dispose();
    _numericalController.dispose();
    super.dispose();
  }

  Future<void> _saveEntry() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final entry = Entry(
        id: widget.existingEntry?.id,
        habitId: widget.habit.id,
        timestamp: widget.date,
        value: widget.habit.type == HabitType.boolean ? _booleanValue : _numericalValue,
        note: _noteController.text.trim().isEmpty ? null : _noteController.text.trim(),
        type: widget.habit.type == HabitType.boolean ? EntryType.boolean : EntryType.numerical,
      );

      // Save to database
      await _databaseService.saveEntry(entry);

      // Update the habit's entries
      widget.habit.addEntry(entry);

      // COMPREHENSIVE DEBUGGING: Save the updated habit
      debugPrint('[ENHANCED_ENTRY_DIALOG] Saving habit after adding entry');
      debugPrint('[ENHANCED_ENTRY_DIALOG] Habit: ${widget.habit.name} (ID: ${widget.habit.id})');
      debugPrint('[ENHANCED_ENTRY_DIALOG] Entry added: ${entry.toJson()}');
      await _databaseService.saveHabit(widget.habit);
      debugPrint('[ENHANCED_ENTRY_DIALOG] Habit saved successfully');

      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save entry: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteEntry() async {
    if (widget.existingEntry == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Delete from database
      await _databaseService.deleteEntry(widget.existingEntry!.id);

      // Remove from habit's entries
      widget.habit.entries.removeWhere((e) => e.id == widget.existingEntry!.id);

      // COMPREHENSIVE DEBUGGING: Save the updated habit after deletion
      debugPrint('[ENHANCED_ENTRY_DIALOG] Saving habit after deleting entry');
      debugPrint('[ENHANCED_ENTRY_DIALOG] Habit: ${widget.habit.name} (ID: ${widget.habit.id})');
      debugPrint('[ENHANCED_ENTRY_DIALOG] Entry deleted: ${widget.existingEntry!.id}');
      await _databaseService.saveHabit(widget.habit);
      debugPrint('[ENHANCED_ENTRY_DIALOG] Habit saved successfully after deletion');

      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete entry: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.habit.name,
                        style: GoogleFonts.inter(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${widget.date.day}/${widget.date.month}/${widget.date.year}',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Entry Input Section
            if (widget.habit.type == HabitType.boolean) ...[
              // Boolean Habit Input
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDark ? Colors.grey[800] : Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      color: theme.colorScheme.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Mark as completed',
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                    Switch(
                      value: _booleanValue,
                      onChanged: (value) {
                        setState(() {
                          _booleanValue = value;
                        });
                      },
                      activeColor: theme.colorScheme.primary,
                    ),
                  ],
                ),
              ),
            ] else ...[
              // Numerical Habit Input
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDark ? Colors.grey[800] : Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.trending_up,
                          color: theme.colorScheme.primary,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Enter value',
                            style: GoogleFonts.inter(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _numericalController,
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                            ],
                            onChanged: (value) {
                              _numericalValue = double.tryParse(value) ?? 0.0;
                            },
                            style: GoogleFonts.inter(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.onSurface,
                            ),
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: theme.colorScheme.outline.withOpacity(0.3),
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: theme.colorScheme.primary,
                                  width: 2,
                                ),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                          ),
                        ),
                        if (widget.habit.unit != null) ...[
                          const SizedBox(width: 8),
                          Text(
                            widget.habit.unit!,
                            style: GoogleFonts.inter(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface.withOpacity(0.6),
                            ),
                          ),
                        ],
                      ],
                    ),
                    if (widget.habit.targetValue != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Target: ${widget.habit.targetValue!.toStringAsFixed(widget.habit.targetValue! % 1 == 0 ? 0 : 1)} ${widget.habit.unit ?? ''}',
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          color: theme.colorScheme.onSurface.withOpacity(0.5),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Notes Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDark ? Colors.grey[800] : Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.note_outlined,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Notes (optional)',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _noteController,
                    maxLines: 3,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: theme.colorScheme.onSurface,
                    ),
                    decoration: InputDecoration(
                      hintText: 'Add a note about this entry...',
                      hintStyle: GoogleFonts.inter(
                        fontSize: 14,
                        color: theme.colorScheme.onSurface.withOpacity(0.4),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: theme.colorScheme.outline.withOpacity(0.3),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: theme.colorScheme.primary,
                          width: 2,
                        ),
                      ),
                      contentPadding: const EdgeInsets.all(12),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Action Buttons
            Row(
              children: [
                if (widget.existingEntry != null) ...[
                  TextButton.icon(
                    onPressed: _isLoading ? null : _deleteEntry,
                    icon: const Icon(Icons.delete_outline, size: 18),
                    label: const Text('Delete'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
                  const Spacer(),
                ] else ...[
                  const Spacer(),
                ],
                TextButton(
                  onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _isLoading ? null : _saveEntry,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(
                          'Save',
                          style: GoogleFonts.inter(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}