import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;

/// Enhanced circular progress widget with animations and color coding
class AnimatedCircularScore extends StatefulWidget {
  final double score; // 0-100
  final double size;
  final Color? color;
  final Duration animationDuration;
  final bool showPercentage;

  const AnimatedCircularScore({
    super.key,
    required this.score,
    this.size = 80,
    this.color,
    this.animationDuration = const Duration(milliseconds: 1500),
    this.showPercentage = true,
  });

  @override
  State<AnimatedCircularScore> createState() => _AnimatedCircularScoreState();
}

class _AnimatedCircularScoreState extends State<AnimatedCircularScore>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: widget.score / 100,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
    
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedCircularScore oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.score != widget.score) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.score / 100,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ));
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = widget.color ?? _getScoreColor(widget.score);
    
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            painter: CircularScorePainter(
              progress: _animation.value,
              color: progressColor,
              backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
              strokeWidth: widget.size * 0.08,
            ),
            child: widget.showPercentage
                ? Center(
                    child: Text(
                      '${(widget.score * _animation.value).round()}%',
                      style: GoogleFonts.inter(
                        fontSize: widget.size * 0.18,
                        fontWeight: FontWeight.w700,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  )
                : null,
          );
        },
      ),
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.blue;
    if (score >= 50) return Colors.orange;
    return Colors.red;
  }
}

/// Custom painter for circular progress
class CircularScorePainter extends CustomPainter {
  final double progress;
  final Color color;
  final Color backgroundColor;
  final double strokeWidth;

  CircularScorePainter({
    required this.progress,
    required this.color,
    required this.backgroundColor,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // Background circle
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Progress arc
    final progressPaint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2, // Start from top
      2 * math.pi * progress,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Linear progress bar with gradient and animation
class AnimatedLinearScore extends StatefulWidget {
  final String habitName;
  final double score; // 0-100
  final Color? color;
  final bool showLabel;
  final double height;

  const AnimatedLinearScore({
    super.key,
    required this.habitName,
    required this.score,
    this.color,
    this.showLabel = true,
    this.height = 8,
  });

  @override
  State<AnimatedLinearScore> createState() => _AnimatedLinearScoreState();
}

class _AnimatedLinearScoreState extends State<AnimatedLinearScore>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: widget.score / 100,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutQuart,
    ));
    
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedLinearScore oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.score != widget.score) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.score / 100,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutQuart,
      ));
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = widget.color ?? _getScoreColor(widget.score);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showLabel) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  widget.habitName,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return Text(
                    '${(widget.score * _animation.value).round()}%',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: progressColor,
                    ),
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 6),
        ],
        Container(
          height: widget.height,
          decoration: BoxDecoration(
            color: theme.colorScheme.outline.withOpacity(0.2),
            borderRadius: BorderRadius.circular(widget.height / 2),
          ),
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return LinearProgressIndicator(
                value: _animation.value,
                backgroundColor: Colors.transparent,
                valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                minHeight: widget.height,
              );
            },
          ),
        ),
      ],
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.blue;
    if (score >= 50) return Colors.orange;
    return Colors.red;
  }
}

/// Mini sparkline chart for showing trend
class MiniSparkline extends StatefulWidget {
  final List<double> scores; // Last 7 days scores
  final double width;
  final double height;
  final Color? color;

  const MiniSparkline({
    super.key,
    required this.scores,
    this.width = 50,
    this.height = 20,
    this.color,
  });

  @override
  State<MiniSparkline> createState() => _MiniSparklineState();
}

class _MiniSparklineState extends State<MiniSparkline>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
    
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final lineColor = widget.color ?? theme.colorScheme.primary;

    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            painter: SparklinePainter(
              scores: widget.scores,
              color: lineColor,
              animation: _animation.value,
            ),
            size: Size(widget.width, widget.height),
          );
        },
      ),
    );
  }
}

/// Custom painter for sparkline
class SparklinePainter extends CustomPainter {
  final List<double> scores;
  final Color color;
  final double animation;

  SparklinePainter({
    required this.scores,
    required this.color,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (scores.isEmpty || scores.length < 2) return;

    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final path = Path();
    final animatedLength = (scores.length * animation).round().clamp(2, scores.length);
    
    // Find min and max for scaling
    final minScore = scores.reduce(math.min);
    final maxScore = scores.reduce(math.max);
    final range = maxScore - minScore;
    
    for (int i = 0; i < animatedLength; i++) {
      final x = (i / (scores.length - 1)) * size.width;
      final normalizedScore = range > 0 ? (scores[i] - minScore) / range : 0.5;
      final y = size.height - (normalizedScore * size.height);
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Enhanced streak display with milestone animations
class AnimatedStreakDisplay extends StatefulWidget {
  final int currentStreak;
  final int bestStreak;
  final bool showMilestoneAnimation;

  const AnimatedStreakDisplay({
    super.key,
    required this.currentStreak,
    required this.bestStreak,
    this.showMilestoneAnimation = true,
  });

  @override
  State<AnimatedStreakDisplay> createState() => _AnimatedStreakDisplayState();
}

class _AnimatedStreakDisplayState extends State<AnimatedStreakDisplay>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _countController;
  late Animation<double> _pulseAnimation;
  late Animation<int> _countAnimation;
  
  int _previousStreak = 0;

  @override
  void initState() {
    super.initState();
    _previousStreak = widget.currentStreak;
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _countController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.elasticOut,
    ));
    
    _countAnimation = IntTween(
      begin: 0,
      end: widget.currentStreak,
    ).animate(CurvedAnimation(
      parent: _countController,
      curve: Curves.easeOutCubic,
    ));
    
    _countController.forward();
  }

  @override
  void didUpdateWidget(AnimatedStreakDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.currentStreak != widget.currentStreak) {
      // Check for milestone
      if (widget.showMilestoneAnimation && _isMilestone(widget.currentStreak)) {
        _pulseController.forward().then((_) {
          _pulseController.reverse();
        });
      }
      
      // Animate count change
      _countAnimation = IntTween(
        begin: _previousStreak,
        end: widget.currentStreak,
      ).animate(CurvedAnimation(
        parent: _countController,
        curve: Curves.easeOutCubic,
      ));
      
      _countController.forward(from: 0);
      _previousStreak = widget.currentStreak;
    }
  }

  bool _isMilestone(int streak) {
    return streak > 0 && (streak == 7 || streak == 30 || streak == 100 || streak % 100 == 0);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _countController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value,
              child: Text(
                '🔥',
                style: TextStyle(
                  fontSize: 20 * _pulseAnimation.value,
                ),
              ),
            );
          },
        ),
        const SizedBox(width: 6),
        AnimatedBuilder(
          animation: _countAnimation,
          builder: (context, child) {
            return Text(
              '${_countAnimation.value}',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: _getStreakColor(widget.currentStreak),
              ),
            );
          },
        ),
        const SizedBox(width: 4),
        Text(
          widget.currentStreak == 1 ? 'day' : 'days',
          style: GoogleFonts.inter(
            fontSize: 14,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Color _getStreakColor(int streak) {
    if (streak >= 100) return Colors.purple;
    if (streak >= 30) return Colors.orange;
    if (streak >= 7) return Colors.green;
    if (streak >= 3) return Colors.blue;
    return Colors.grey;
  }
}

/// Compact score and streak widget for list items
class CompactHabitMetrics extends StatelessWidget {
  final double score;
  final int streak;
  final bool showSparkline;
  final List<double>? scoreHistory;

  const CompactHabitMetrics({
    super.key,
    required this.score,
    required this.streak,
    this.showSparkline = false,
    this.scoreHistory,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Score
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: _getScoreColor(score).withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            '${score.round()}%',
            style: GoogleFonts.inter(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: _getScoreColor(score),
            ),
          ),
        ),
        
        if (streak > 0) ...[
          const SizedBox(width: 6),
          // Streak
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            decoration: BoxDecoration(
              color: _getStreakColor(streak).withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('🔥', style: TextStyle(fontSize: 8)),
                const SizedBox(width: 2),
                Text(
                  '$streak',
                  style: GoogleFonts.inter(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: _getStreakColor(streak),
                  ),
                ),
              ],
            ),
          ),
        ],
        
        if (showSparkline && scoreHistory != null && scoreHistory!.isNotEmpty) ...[
          const SizedBox(width: 8),
          MiniSparkline(
            scores: scoreHistory!,
            width: 40,
            height: 16,
            color: theme.colorScheme.primary.withOpacity(0.7),
          ),
        ],
      ],
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 70) return Colors.blue;
    if (score >= 50) return Colors.orange;
    return Colors.red;
  }

  Color _getStreakColor(int streak) {
    if (streak >= 30) return Colors.purple;
    if (streak >= 14) return Colors.orange;
    if (streak >= 7) return Colors.green;
    if (streak >= 3) return Colors.blue;
    return Colors.grey;
  }
}