import 'dart:math' as math;
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'habit.dart';
import 'entry.dart';
import 'settings_service.dart';

/// Time scales for analytics charts
enum TimeScale {
  day,
  week,
  month,
  quarter,
  year,
}

/// Data point for charts
class ChartDataPoint {
  final DateTime date;
  final double value;
  final String label;
  final bool isFuture; // Add this new field

  ChartDataPoint({
    required this.date,
    required this.value,
    required this.label,
    this.isFuture = false, // Default to false
  });
}

/// Service for calculating habit analytics and providing data for charts
class HabitAnalyticsService {
  final Habit habit;
  final List<Entry> entries;

  HabitAnalyticsService({
    required this.habit,
    required this.entries,
  }) {
    developer.log('DEBUG Analytics | === ANALYTICS SERVICE CREATED ===');
    developer.log('DEBUG Analytics | Habit: ${habit.name} (ID: ${habit.id})');
    developer.log('DEBUG Analytics | Entries provided: ${entries.length}');
    developer.log('DEBUG Analytics | Habit.entries: ${habit.entries.length}');
    
    // Show sample entries
    for (int i = 0; i < entries.length && i < 3; i++) {
      developer.log('DEBUG Analytics | Entry $i: ${entries[i].timestamp} - ${entries[i].boolValue}');
    }
  }

  /// Calculate the current week number based on user's start of week preference
  /// Returns a formatted string like "W28"
  static Future<String> getCurrentWeekNumber(SettingsService settingsService) async {
    final now = DateTime.now();
    final startOfWeekDay = await settingsService.getStartOfWeek();
    
    // Calculate the week number based on the user's start day preference
    int weekNumber;
    
    if (startOfWeekDay == SettingsService.SUNDAY) {
      // Sunday-based week calculation
      final startOfYear = DateTime(now.year, 1, 1);
      final firstSunday = startOfYear.subtract(Duration(days: startOfYear.weekday % 7));
      final daysSinceFirstSunday = now.difference(firstSunday).inDays;
      weekNumber = (daysSinceFirstSunday / 7).floor() + 1;
    } else {
      // Monday-based week calculation (ISO 8601)
      final startOfYear = DateTime(now.year, 1, 1);
      final firstMonday = startOfYear.subtract(Duration(days: (startOfYear.weekday - 1) % 7));
      final daysSinceFirstMonday = now.difference(firstMonday).inDays;
      weekNumber = (daysSinceFirstMonday / 7).floor() + 1;
    }
    
    // Ensure week number is within valid range (1-53)
    weekNumber = weekNumber.clamp(1, 53);
    
    final startDayName = startOfWeekDay == SettingsService.SUNDAY ? 'Sunday' : 'Monday';
    developer.log('DEBUG WeekCalc | Start Day: $startDayName | Calculated Week: W$weekNumber');
    
    return 'W$weekNumber';
  }

  /// Calculate habit strength score using the precise formula from habit_metrics.md
  /// score = previousScore × multiplier + checkmarkValue × (1 − multiplier)
  /// NOTE: This method is renamed from calculateScore to avoid confusion with percentage calculations
  /// For simple completion percentages, use calculateCompletionPercentage() instead
  double calculateHabitStrengthScore(TimeScale timeScale) {
    final now = DateTime.now();
    
    if (timeScale == TimeScale.week) {
      // PHASE 2: For week calculations, use legacy logic here but recommend using calculateThisWeekPercentage()
      // This maintains compatibility while the new async method provides accurate calendar week calculations
      final currentWeekday = now.weekday; // 1 = Monday, 7 = Sunday
      final daysFromMonday = currentWeekday - 1;
      final effectiveStartDate = DateTime(now.year, now.month, now.day - daysFromMonday);
      final effectiveEndDate = DateTime(now.year, now.month, now.day + 1);
      
      debugPrint('[HABIT_ANALYTICS] Legacy week boundaries: ${effectiveStartDate} to ${effectiveEndDate}');
      
      // Filter entries within the time range
      final relevantEntries = entries.where((entry) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        return (entryDate.isAfter(effectiveStartDate) || entryDate.isAtSameMomentAs(effectiveStartDate)) && 
               entryDate.isBefore(effectiveEndDate);
      }).toList();

      if (relevantEntries.isEmpty) return 0.0;

      // Sort entries by date
      relevantEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      double score = 0.0;
      final multiplier = _getMultiplierForHabitFrequency();

      for (final entry in relevantEntries) {
        final checkmarkValue = _getCheckmarkValue(entry);
        score = score * multiplier + checkmarkValue * (1 - multiplier);
      }

      return score.clamp(0.0, 1.0);
    } else {
      // For non-week timescales, use original logic
      final startDate = _getStartDateForTimeScale(now, timeScale);
      final effectiveEndDate = now.add(const Duration(days: 1));
      
      final relevantEntries = entries.where((entry) {
        return entry.timestamp.isAfter(startDate) && 
               entry.timestamp.isBefore(effectiveEndDate);
      }).toList();

      if (relevantEntries.isEmpty) return 0.0;

      relevantEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      double score = 0.0;
      final multiplier = _getMultiplierForHabitFrequency();

      for (final entry in relevantEntries) {
        final checkmarkValue = _getCheckmarkValue(entry);
        score = score * multiplier + checkmarkValue * (1 - multiplier);
      }

      return score.clamp(0.0, 1.0);
    }
  }

  /// Get multiplier based on habit frequency (daily habits have different multiplier than weekly)
  double _getMultiplierForHabitFrequency() {
    // For daily habits, use 0.95 (retains 95% of previous score)
    // This can be customized based on habit frequency settings
    return 0.95;
  }

  /// Convert entry to checkmark value (0.0 or 1.0 for boolean, percentage for numerical)
  double _getCheckmarkValue(Entry entry) {
    if (entry.type == EntryType.boolean) {
      return entry.boolValue ? 1.0 : 0.0;
    } else if (entry.type == EntryType.numerical && habit.targetValue != null) {
      final value = entry.numericalValue ?? 0.0;
      return (value / habit.targetValue!).clamp(0.0, 1.0);
    }
    return 0.0;
  }

  /// Calculate current streak with start and end dates
  Map<String, dynamic> calculateCurrentStreakWithDates() {
    if (entries.isEmpty) return {'length': 0, 'startDate': null, 'endDate': null};

    final sortedEntries = List<Entry>.from(entries);
    sortedEntries.sort((a, b) => b.timestamp.compareTo(a.timestamp)); // Most recent first

    int streak = 0;
    DateTime currentDate = DateTime.now();
    DateTime? streakStartDate;
    DateTime? streakEndDate;
    
    for (int i = 0; i < 365; i++) { // Check up to a year back
      final dateToCheck = DateTime(currentDate.year, currentDate.month, currentDate.day);
      final entryForDate = sortedEntries.where((entry) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        return entryDate == dateToCheck;
      }).firstOrNull;

      if (entryForDate != null && _isEntryCompleted(entryForDate)) {
        streak++;
        if (streakEndDate == null) {
          streakEndDate = dateToCheck; // First completed day (most recent)
        }
        streakStartDate = dateToCheck; // Keep updating to get the earliest day
      } else {
        break;
      }

      currentDate = currentDate.subtract(const Duration(days: 1));
    }

    return {
      'length': streak,
      'startDate': streakStartDate,
      'endDate': streakEndDate,
    };
  }

  /// Calculate current streak (backward compatibility)
  int calculateCurrentStreak() {
    final result = calculateCurrentStreakWithDates();
    return result['length'] as int;
  }

  /// Calculate best streak with start and end dates
  Map<String, dynamic> calculateBestStreakWithDates() {
    if (entries.isEmpty) return {'length': 0, 'startDate': null, 'endDate': null};

    final sortedEntries = List<Entry>.from(entries);
    sortedEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    int bestStreak = 0;
    int currentStreak = 0;
    DateTime? lastDate;
    DateTime? bestStreakStartDate;
    DateTime? bestStreakEndDate;
    DateTime? currentStreakStartDate;

    for (final entry in sortedEntries) {
      if (_isEntryCompleted(entry)) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        
        if (lastDate == null || entryDate.difference(lastDate).inDays == 1) {
          if (currentStreak == 0) {
            currentStreakStartDate = entryDate;
          }
          currentStreak++;
          
          if (currentStreak > bestStreak) {
            bestStreak = currentStreak;
            bestStreakStartDate = currentStreakStartDate;
            bestStreakEndDate = entryDate;
          }
        } else if (entryDate.difference(lastDate!).inDays > 1) {
          currentStreak = 1;
          currentStreakStartDate = entryDate;
          
          if (currentStreak > bestStreak) {
            bestStreak = currentStreak;
            bestStreakStartDate = currentStreakStartDate;
            bestStreakEndDate = entryDate;
          }
        }
        
        lastDate = entryDate;
      } else {
        currentStreak = 0;
        currentStreakStartDate = null;
      }
    }

    return {
      'length': bestStreak,
      'startDate': bestStreakStartDate,
      'endDate': bestStreakEndDate,
    };
  }

  /// Calculate best streak (backward compatibility)
  int calculateBestStreak() {
    final result = calculateBestStreakWithDates();
    return result['length'] as int;
  }

  /// Check if an entry represents a completion
  bool _isEntryCompleted(Entry entry) {
    developer.log('DEBUG Completion | Checking entry: ${entry.timestamp} - type: ${entry.type} - boolValue: ${entry.boolValue}');
    
    if (entry.type == EntryType.boolean) {
      final result = entry.boolValue;
      developer.log('DEBUG Completion | Boolean entry result: $result');
      return result;
    } else if (entry.type == EntryType.numerical && habit.targetValue != null) {
      final value = entry.numericalValue ?? 0.0;
      final result = value >= (habit.targetValue! * 0.8); // 80% of target considered complete
      developer.log('DEBUG Completion | Numerical entry result: $result (value: $value, target: ${habit.targetValue})');
      return result;
    }
    developer.log('DEBUG Completion | Entry not completed (unknown type or no target)');
    return false;
  }

  /// Calculate completion percentage for current month
  /// TASK 3: Fixed to use strict date boundaries
  double calculateMonthCompletionPercentage() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    return _calculateCompletionPercentageForPeriod(startOfMonth, endOfMonth);
  }

  /// Calculate completion percentage for current year
  /// TASK 3: Fixed to use strict date boundaries
  double calculateYearCompletionPercentage() {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final endOfYear = DateTime(now.year, 12, 31);
    
    return _calculateCompletionPercentageForPeriod(startOfYear, endOfYear);
  }

  /// Calculate completion percentage for a specific period
  /// FIXED: Correctly calculates percentage for all time periods
  double _calculateCompletionPercentageForPeriod(DateTime start, DateTime end) {
    developer.log('DEBUG Percentage | === CALCULATING COMPLETION PERCENTAGE ===');
    developer.log('DEBUG Percentage | Period: $start to $end');
    developer.log('DEBUG Percentage | Available entries: ${entries.length}');
    
    // Debug: Show all available entries
    for (int i = 0; i < entries.length && i < 5; i++) {
      developer.log('DEBUG Percentage | Entry $i: ${entries[i].timestamp} - completed: ${_isEntryCompleted(entries[i])}');
    }
    
    int totalDays = 0;
    int completedDays = 0;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    DateTime current = start;
    while (current.isBefore(end.add(const Duration(days: 1)))) {
      final currentDate = DateTime(current.year, current.month, current.day);
      
      // For future periods, don't include any days
      // For current periods, only count days up to today
      // For past periods, count all days in the period
      final periodEnd = DateTime(end.year, end.month, end.day);
      
      developer.log('DEBUG Percentage | Current date: $currentDate, Today: $today, Period end: $periodEnd');
      
      if (periodEnd.isBefore(today)) {
        // Past period: count all days in the period
        totalDays++;
        developer.log('DEBUG Percentage | Past period - counting day: $currentDate');
      } else if (currentDate.isAfter(today)) {
        // Future date: don't count
        developer.log('DEBUG Percentage | Future date - skipping: $currentDate');
        break;
      } else {
        // Current period: only count up to today
        totalDays++;
        developer.log('DEBUG Percentage | Current period - counting day: $currentDate');
      }
      
      final entryForDate = entries.where((entry) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        developer.log('DEBUG Percentage | Comparing entry date $entryDate with current date $currentDate');
        return entryDate == currentDate;
      }).firstOrNull;

      if (entryForDate != null && _isEntryCompleted(entryForDate)) {
        completedDays++;
        developer.log('DEBUG Percentage | Found completion on $currentDate');
      } else {
        developer.log('DEBUG Percentage | No completion found for $currentDate');
      }
      
      current = current.add(const Duration(days: 1));
    }

    final percentage = totalDays > 0 ? (completedDays / totalDays) * 100 : 0.0;
    developer.log('DEBUG Percentage | Result: $completedDays/$totalDays = ${percentage.toStringAsFixed(1)}%');
    
    return percentage;
  }

  /// Calculate total completions
  int calculateTotalCompletions() {
    return entries.where(_isEntryCompleted).length;
  }

  /// Get frequency breakdown by day of week
  Map<String, int> calculateFrequencyByDayOfWeek() {
    final frequency = <String, int>{
      'Mon': 0,
      'Tue': 0,
      'Wed': 0,
      'Thu': 0,
      'Fri': 0,
      'Sat': 0,
      'Sun': 0,
    };

    final dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    for (final entry in entries) {
      if (_isEntryCompleted(entry)) {
        final dayOfWeek = entry.timestamp.weekday - 1; // Monday = 0
        frequency[dayNames[dayOfWeek]] = frequency[dayNames[dayOfWeek]]! + 1;
      }
    }

    return frequency;
  }

  /// TASK 1: Calculate frequency by day of week returning Map<int, int>
  /// where key is weekday (1 for Monday, 7 for Sunday) and value is completion count
  Map<int, int> calculateFrequencyByDay() {
    final frequency = <int, int>{
      1: 0, // Monday
      2: 0, // Tuesday
      3: 0, // Wednesday
      4: 0, // Thursday
      5: 0, // Friday
      6: 0, // Saturday
      7: 0, // Sunday
    };

    for (final entry in entries) {
      if (_isEntryCompleted(entry)) {
        final weekday = entry.timestamp.weekday; // 1-7 (Mon-Sun)
        final adjustedWeekday = weekday == 7 ? 7 : weekday; // Keep Sunday as 7
        frequency[adjustedWeekday] = frequency[adjustedWeekday]! + 1;
      }
    }

    return frequency;
  }

  /// Get score data for chart - FIXED: Now uses completion percentages instead of cumulative scores
  /// This ensures the Score Chart shows the same accurate data as the "This Week %" card
  Future<List<ChartDataPoint>> getScoreDataForChart(TimeScale timeScale) async {
    developer.log('DEBUG Score Chart | === getScoreDataForChart CALLED ===');
    developer.log('DEBUG Score Chart | Time scale: ${timeScale.name}');
    developer.log('DEBUG Score Chart | Habit: ${habit.name}');
    developer.log('DEBUG Score Chart | Entries count: ${entries.length}');
    
    final now = DateTime.now();
    final dataPoints = <ChartDataPoint>[];

    if (timeScale == TimeScale.week) {
      // For weekly data, use proper calendar week boundaries like the History Chart
      developer.log('DEBUG Score Chart | Using weekly score data generation');
      final weeklyData = await _getWeeklyScoreData(now);
      developer.log('DEBUG Score Chart | Generated ${weeklyData.length} weekly score data points');
      return weeklyData;
    } else {
      // For other time scales, use the same logic as History Chart but convert to percentages
      switch (timeScale) {
        case TimeScale.day:
          // REFINEMENT: Generate daily completion percentages for fewer days to reduce crowding
          final startDate = now.subtract(const Duration(days: 7)); // Reduced from 30 to 7 days
          final endDate = now.add(const Duration(days: 4)); // Reduced from 7 to 4 future days
          
          DateTime current = DateTime(startDate.year, startDate.month, startDate.day);
          while (current.isBefore(endDate.add(const Duration(days: 1)))) {
            final isCompleted = habit.isCompletedOnDate(current);
            final percentage = isCompleted ? 100.0 : 0.0; // BUGFIX: Use 0-100 range for Score Chart
            final bool isFutureDate = current.isAfter(DateTime(now.year, now.month, now.day));
            dataPoints.add(ChartDataPoint(
              date: current,
              value: percentage,
              label: _formatDateForTimeScale(current, timeScale),
              isFuture: isFutureDate,
            ));
            current = current.add(const Duration(days: 1));
          }
          break;
          
        case TimeScale.month:
          // Generate monthly completion percentages for the last 12 months plus 3 future months
          // FIXED: Adjust date range based on actual entry dates
          DateTime startDate = DateTime(now.year - 1, now.month, 1);
          DateTime endDate = DateTime(now.year, now.month + 4, 1);
          
          // If we have entries, adjust the range to include them
          if (entries.isNotEmpty) {
            final earliestEntry = entries.map((e) => e.timestamp).reduce((a, b) => a.isBefore(b) ? a : b);
            final latestEntry = entries.map((e) => e.timestamp).reduce((a, b) => a.isAfter(b) ? a : b);
            
            // Expand range to include entry dates
            final entryStartMonth = DateTime(earliestEntry.year, earliestEntry.month, 1);
            final entryEndMonth = DateTime(latestEntry.year, latestEntry.month + 1, 1);
            
            if (entryStartMonth.isBefore(startDate)) {
              startDate = entryStartMonth;
            }
            if (entryEndMonth.isAfter(endDate)) {
              endDate = entryEndMonth;
            }
          }
          
          developer.log('DEBUG Month | === MONTH CALCULATION START ===');
          developer.log('DEBUG Month | Date range: $startDate to $endDate');
          developer.log('DEBUG Month | Available entries: ${entries.length}');
          
          DateTime current = startDate;
          while (current.isBefore(endDate)) {
            final monthEnd = DateTime(current.year, current.month + 1, 1).subtract(const Duration(days: 1));
            developer.log('DEBUG Month | Calculating for month: $current to $monthEnd');
            
            // BUGFIX: Calculate percentage for this specific month, not current month
            final percentage = _calculateCompletionPercentageForPeriod(current, monthEnd);
            
            developer.log('DEBUG Month | Month ${current.month}/${current.year}: ${percentage.toStringAsFixed(1)}%');
            developer.log('DEBUG Month | Period: $current to $monthEnd');
            developer.log('DEBUG Month | Entries in period: ${entries.where((e) => e.timestamp.isAfter(current.subtract(Duration(days: 1))) && e.timestamp.isBefore(monthEnd.add(Duration(days: 1)))).length}');
            
            final bool isFutureDate = current.isAfter(DateTime(now.year, now.month, 1));
            dataPoints.add(ChartDataPoint(
              date: current,
              value: percentage, // BUGFIX: Keep 0-100 range for Foundation Score Chart consistency
              label: _formatDateForTimeScale(current, timeScale),
              isFuture: isFutureDate,
            ));
            current = DateTime(current.year, current.month + 1, 1);
          }
          break;
          
        case TimeScale.quarter:
          // Generate quarterly completion percentages
          final currentQuarter = ((now.month - 1) ~/ 3) + 1;
          final startYear = now.year - 2;
          final startQuarter = currentQuarter;
          
          DateTime current = DateTime(startYear, (startQuarter - 1) * 3 + 1, 1);
          final endDate = DateTime(now.year + 1, (currentQuarter - 1) * 3 + 1, 1);
          
          while (current.isBefore(endDate)) {
            final quarterEndMonth = current.month + 2;
            final quarterEnd = DateTime(current.year, quarterEndMonth + 1, 1).subtract(const Duration(days: 1));
            // BUGFIX: Calculate percentage for this specific quarter, not current quarter
            final percentage = _calculateCompletionPercentageForPeriod(current, quarterEnd);
            
            developer.log('DEBUG Quarter | Quarter ${current.year}-Q${((current.month - 1) ~/ 3) + 1}: ${percentage.toStringAsFixed(1)}%');
            developer.log('DEBUG Quarter | Period: $current to $quarterEnd');
            
            final currentQuarterNum = ((current.month - 1) ~/ 3) + 1;
            final nowQuarterNum = ((now.month - 1) ~/ 3) + 1;
            final bool isFutureDate = current.year > now.year || 
                                    (current.year == now.year && currentQuarterNum > nowQuarterNum);
            
            developer.log('DEBUG Quarterly Score | Quarter: ${current.year}-Q${currentQuarterNum}, Percentage: ${percentage.toStringAsFixed(1)}%');
            
            dataPoints.add(ChartDataPoint(
              date: current,
              value: percentage, // BUGFIX: Keep 0-100 range for Foundation Score Chart
              label: _formatDateForTimeScale(current, timeScale),
              isFuture: isFutureDate,
            ));
            
            // Move to next quarter
            final nextQuarterMonth = current.month + 3;
            if (nextQuarterMonth > 12) {
              current = DateTime(current.year + 1, nextQuarterMonth - 12, 1);
            } else {
              current = DateTime(current.year, nextQuarterMonth, 1);
            }
          }
          break;
          
        case TimeScale.year:
          // Generate yearly completion percentages for the last 5 years plus 2 future years
          final startYear = now.year - 5;
          final endYear = now.year + 3;
          
          for (int year = startYear; year < endYear; year++) {
            final yearStart = DateTime(year, 1, 1);
            final yearEnd = DateTime(year, 12, 31);
            final percentage = _calculateCompletionPercentageForPeriod(yearStart, yearEnd);
            final bool isFutureDate = year > now.year;
            dataPoints.add(ChartDataPoint(
              date: yearStart,
              value: percentage, // BUGFIX: Keep 0-100 range for Foundation Score Chart
              label: _formatDateForTimeScale(yearStart, timeScale),
              isFuture: isFutureDate,
            ));
          }
          break;
          
        default:
          // Fallback - should not happen
          break;
      }

      developer.log('DEBUG Score Chart | Generated ${dataPoints.length} percentage data points for ${timeScale.name} view');
      return dataPoints;
    }
  }

  /// Generate weekly score data using proper calendar week boundaries (like History Chart)
  Future<List<ChartDataPoint>> _getWeeklyScoreData(DateTime now) async {
    final dataPoints = <ChartDataPoint>[];
    
    // Get the current week boundaries
    final currentWeekBoundaries = await getWeekBoundaries(now);
    final currentWeekStart = currentWeekBoundaries['startDate']!;
    
    // FIXED: Generate data for weeks that include actual entries
    int startOffset = -12;
    int endOffset = 4;
    
    // If we have entries, adjust the range to include them
    if (entries.isNotEmpty) {
      final earliestEntry = entries.map((e) => e.timestamp).reduce((a, b) => a.isBefore(b) ? a : b);
      final latestEntry = entries.map((e) => e.timestamp).reduce((a, b) => a.isAfter(b) ? a : b);
      
      // Calculate week offsets for entry dates
      final earliestWeekOffset = earliestEntry.difference(currentWeekStart).inDays ~/ 7;
      final latestWeekOffset = latestEntry.difference(currentWeekStart).inDays ~/ 7;
      
      // Expand range to include entry weeks
      if (earliestWeekOffset < startOffset) {
        startOffset = earliestWeekOffset - 1; // Add buffer
      }
      if (latestWeekOffset > endOffset) {
        endOffset = latestWeekOffset + 1; // Add buffer
      }
    }
    
    developer.log('DEBUG Weekly | Week range: $startOffset to $endOffset');
    
    // Generate data for the calculated week range
    for (int weekOffset = startOffset; weekOffset <= endOffset; weekOffset++) {
      final weekStart = currentWeekStart.add(Duration(days: weekOffset * 7));
      final weekEnd = weekStart.add(const Duration(days: 6));
      
      // Calculate completion percentage for this week
      developer.log('DEBUG Weekly | === WEEK CALCULATION ===');
      developer.log('DEBUG Weekly | Week period: $weekStart to $weekEnd');
      developer.log('DEBUG Weekly | Available entries: ${entries.length}');
      
      final percentage = _calculateCompletionPercentageForPeriod(weekStart, weekEnd);
      
      developer.log('DEBUG Weekly | Week result: ${percentage.toStringAsFixed(1)}%');
      
      final bool isFutureDate = weekStart.isAfter(now);
      
      // Generate proper week label using the week start date
      final weekLabel = await _getWeekLabel(weekStart);
      
      dataPoints.add(ChartDataPoint(
        date: weekStart,
        value: percentage, // BUGFIX: Keep 0-100 range for Score Chart consistency
        label: weekLabel,
        isFuture: isFutureDate,
      ));
      
      developer.log('DEBUG Weekly Score | Week starting $weekStart: ${percentage.toStringAsFixed(1)}% (Label: $weekLabel)');
    }
    
    return dataPoints;
  }

  /// Get history data for bar chart
  Future<List<ChartDataPoint>> getHistoryDataForChart(TimeScale timeScale) async {
    developer.log('DEBUG History Chart | === getHistoryDataForChart CALLED ===');
    developer.log('DEBUG History Chart | Time scale: ${timeScale.name}');
    developer.log('DEBUG History Chart | Habit: ${habit.name}');
    developer.log('DEBUG History Chart | Entries count: ${entries.length}');
    
    final now = DateTime.now();
    final dataPoints = <ChartDataPoint>[];

    if (timeScale == TimeScale.week) {
      // BUGFIX: For weekly data, use proper calendar week boundaries
      developer.log('DEBUG History Chart | Using weekly data generation');
      final weeklyData = await _getWeeklyHistoryData(now);
      developer.log('DEBUG History Chart | Generated ${weeklyData.length} weekly data points');
      return weeklyData;
    } else {
      // Fixed logic for Day, Month, Quarter, and Year views
      switch (timeScale) {
        case TimeScale.day:
          // REFINEMENT: Generate daily data points for fewer days to reduce crowding (11 total points)
          final startDate = now.subtract(const Duration(days: 7)); // Reduced from 30 to 7 days
          final endDate = now.add(const Duration(days: 4)); // Reduced from 7 to 4 future days
          
          DateTime current = DateTime(startDate.year, startDate.month, startDate.day);
          while (current.isBefore(endDate.add(const Duration(days: 1)))) {
            // BUGFIX: For daily view, show 0 or 1 (not completed or completed)
            final isCompleted = habit.isCompletedOnDate(current);
            final completions = isCompleted ? 1 : 0;
            final bool isFutureDate = current.isAfter(DateTime(now.year, now.month, now.day));
            dataPoints.add(ChartDataPoint(
              date: current,
              value: completions.toDouble(),
              label: _formatDateForTimeScale(current, timeScale),
              isFuture: isFutureDate,
            ));
            current = current.add(const Duration(days: 1));
          }
          break;
          
        case TimeScale.month:
          // Generate monthly data points for the last 12 months plus 3 future months
          final startDate = DateTime(now.year - 1, now.month, 1);
          final endDate = DateTime(now.year, now.month + 4, 1);
          
          DateTime current = startDate;
          while (current.isBefore(endDate)) {
            final completions = _getCompletionsForPeriod(current, timeScale);
            final bool isFutureDate = current.isAfter(DateTime(now.year, now.month, 1));
            dataPoints.add(ChartDataPoint(
              date: current,
              value: completions.toDouble(),
              label: _formatDateForTimeScale(current, timeScale),
              isFuture: isFutureDate,
            ));
            current = DateTime(current.year, current.month + 1, 1);
          }
          break;
          
        case TimeScale.quarter:
          // Generate quarterly data points for the last 8 quarters plus 2 future quarters
          final currentQuarter = ((now.month - 1) ~/ 3) + 1;
          final startYear = now.year - 2;
          final startQuarter = currentQuarter;
          
          DateTime current = DateTime(startYear, (startQuarter - 1) * 3 + 1, 1);
          final endDate = DateTime(now.year + 1, (currentQuarter - 1) * 3 + 1, 1);
          
          while (current.isBefore(endDate)) {
            final completions = _getCompletionsForPeriod(current, timeScale);
            final currentQuarterNum = ((current.month - 1) ~/ 3) + 1;
            final nowQuarterNum = ((now.month - 1) ~/ 3) + 1;
            final bool isFutureDate = current.year > now.year || 
                                    (current.year == now.year && currentQuarterNum > nowQuarterNum);
            dataPoints.add(ChartDataPoint(
              date: current,
              value: completions.toDouble(),
              label: _formatDateForTimeScale(current, timeScale),
              isFuture: isFutureDate,
            ));
            
            // Move to next quarter
            final nextQuarterMonth = current.month + 3;
            if (nextQuarterMonth > 12) {
              current = DateTime(current.year + 1, nextQuarterMonth - 12, 1);
            } else {
              current = DateTime(current.year, nextQuarterMonth, 1);
            }
          }
          break;
          
        case TimeScale.year:
          // Generate yearly data points for the last 5 years plus 2 future years
          final startYear = now.year - 5;
          final endYear = now.year + 3;
          
          for (int year = startYear; year < endYear; year++) {
            final yearStart = DateTime(year, 1, 1);
            final completions = _getCompletionsForPeriod(yearStart, timeScale);
            final bool isFutureDate = year > now.year;
            dataPoints.add(ChartDataPoint(
              date: yearStart,
              value: completions.toDouble(),
              label: _formatDateForTimeScale(yearStart, timeScale),
              isFuture: isFutureDate,
            ));
          }
          break;
          
        default:
          // Fallback to original logic for any unhandled cases
          final startDate = _getStartDateForTimeScale(now, timeScale);
          final futureDuration = const Duration(days: 28);
          final endDate = now.add(futureDuration);
          
          DateTime current = startDate;
          while (current.isBefore(endDate.add(const Duration(days: 1)))) {
            final completions = _getCompletionsForPeriod(current, timeScale);
            final bool isFutureDate = current.isAfter(now);
            dataPoints.add(ChartDataPoint(
              date: current,
              value: completions.toDouble(),
              label: _formatDateForTimeScale(current, timeScale),
              isFuture: isFutureDate,
            ));
            current = _getNextDateForTimeScale(current, timeScale);
          }
          break;
      }

      developer.log('DEBUG History Chart | Generated ${dataPoints.length} data points for ${timeScale.name} view');
      return dataPoints;
    }
  }

  /// Generate weekly history data using proper calendar week boundaries
  Future<List<ChartDataPoint>> _getWeeklyHistoryData(DateTime now) async {
    final dataPoints = <ChartDataPoint>[];
    final settingsService = SettingsService.instance;
    
    // Get the current week boundaries
    final currentWeekBoundaries = await getWeekBoundaries(now);
    final currentWeekStart = currentWeekBoundaries['startDate']!;
    
    // Generate data for the last 12 weeks plus 4 future weeks
    for (int weekOffset = -12; weekOffset <= 4; weekOffset++) {
      final weekStart = currentWeekStart.add(Duration(days: weekOffset * 7));
      final completions = _getCompletionsForWeek(weekStart);
      final bool isFutureDate = weekStart.isAfter(now);
      
      // Generate proper week label using the week start date
      final weekLabel = await _getWeekLabel(weekStart);
      
      dataPoints.add(ChartDataPoint(
        date: weekStart,
        value: completions.toDouble(),
        label: weekLabel,
        isFuture: isFutureDate,
      ));
      
      developer.log('DEBUG Weekly History | Week starting $weekStart: $completions completions (Label: $weekLabel)');
    }
    
    return dataPoints;
  }

  /// Generate proper week label based on user's start of week preference
  Future<String> _getWeekLabel(DateTime weekStartDate) async {
    final settingsService = SettingsService.instance;
    final startOfWeekDay = await settingsService.getStartOfWeek();
    
    // Calculate week number based on user's start of week preference
    int weekNumber;
    
    if (startOfWeekDay == SettingsService.SUNDAY) {
      // Sunday-based week calculation
      final startOfYear = DateTime(weekStartDate.year, 1, 1);
      final firstSunday = startOfYear.subtract(Duration(days: startOfYear.weekday % 7));
      final daysSinceFirstSunday = weekStartDate.difference(firstSunday).inDays;
      weekNumber = (daysSinceFirstSunday / 7).floor() + 1;
    } else {
      // Monday-based week calculation (ISO 8601)
      final startOfYear = DateTime(weekStartDate.year, 1, 1);
      final firstMonday = startOfYear.subtract(Duration(days: (startOfYear.weekday - 1) % 7));
      final daysSinceFirstMonday = weekStartDate.difference(firstMonday).inDays;
      weekNumber = (daysSinceFirstMonday / 7).floor() + 1;
    }
    
    // Ensure week number is within valid range (1-53)
    weekNumber = weekNumber.clamp(1, 53);
    
    return 'W$weekNumber';
  }

  /// Get completions for calendar
  Map<DateTime, bool> getCompletionsForCalendar() {
    final completions = <DateTime, bool>{};

    for (final entry in entries) {
      final date = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
      completions[date] = _isEntryCompleted(entry);
    }

    return completions;
  }

  /// Task 3: Get heatmap data as a Set<DateTime> of all completion dates
  /// This method provides the completion dates for the ActivityHeatmap widget
  Set<DateTime> getHeatmapData() {
    final completionDates = <DateTime>{};

    for (final entry in entries) {
      if (_isEntryCompleted(entry)) {
        final date = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        completionDates.add(date);
      }
    }

    debugPrint('[HABIT_ANALYTICS] Generated heatmap data with ${completionDates.length} completion dates');
    return completionDates;
  }

  /// Helper methods for date calculations
  DateTime _getStartDateForTimeScale(DateTime date, TimeScale timeScale) {
    switch (timeScale) {
      case TimeScale.day:
        return date.subtract(const Duration(days: 30)); // Last 30 days
      case TimeScale.week:
        return date.subtract(const Duration(days: 84)); // Last 12 weeks
      case TimeScale.month:
        return DateTime(date.year - 1, date.month, date.day); // Last 12 months
      case TimeScale.quarter:
        return DateTime(date.year - 2, date.month, date.day); // Last 8 quarters
      case TimeScale.year:
        return DateTime(date.year - 5, date.month, date.day); // Last 5 years
    }
  }

  DateTime _getNextDateForTimeScale(DateTime date, TimeScale timeScale) {
    switch (timeScale) {
      case TimeScale.day:
        return date.add(const Duration(days: 1));
      case TimeScale.week:
        return date.add(const Duration(days: 7));
      case TimeScale.month:
        return DateTime(date.year, date.month + 1, date.day);
      case TimeScale.quarter:
        return DateTime(date.year, date.month + 3, date.day);
      case TimeScale.year:
        return DateTime(date.year + 1, date.month, date.day);
    }
  }

  String _formatDateForTimeScale(DateTime date, TimeScale timeScale) {
    switch (timeScale) {
      case TimeScale.day:
        return _formatDayLabel(date);
      case TimeScale.week:
        return 'W${_getWeekOfYear(date)}';
      case TimeScale.month:
        return _formatMonthLabel(date);
      case TimeScale.quarter:
        return _formatQuarterLabel(date);
      case TimeScale.year:
        return date.year.toString();
    }
  }

  /// Format day labels with date and day of week for enhanced context
  /// Returns a two-line format: date on top, day abbreviation below
  String _formatDayLabel(DateTime date) {
    const dayAbbreviations = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];
    final dayAbbr = dayAbbreviations[date.weekday - 1]; // weekday is 1-7 (Mon-Sun)
    
    // Return format: "day\ndayAbbr" for two-line display
    return '${date.day}\n$dayAbbr';
  }

  /// Format month labels with three-letter abbreviations and year context
  /// Shows full year for January to provide clear year transition context
  String _formatMonthLabel(DateTime date) {
    const monthAbbreviations = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    final monthAbbr = monthAbbreviations[date.month - 1];
    
    // Show full year for January to provide clear year transition context
    if (date.month == 1) {
      return date.year.toString();
    }
    
    return monthAbbr;
  }

  /// Format quarter labels using the end month of each quarter
  /// Q1 (Jan-Mar) -> "Mar", Q2 (Apr-Jun) -> "Jun", etc.
  String _formatQuarterLabel(DateTime date) {
    final quarter = ((date.month - 1) ~/ 3) + 1;
    
    switch (quarter) {
      case 1: // Q1: Jan-Mar
        return 'Mar';
      case 2: // Q2: Apr-Jun
        return 'Jun';
      case 3: // Q3: Jul-Sep
        return 'Sep';
      case 4: // Q4: Oct-Dec
        return 'Dec';
      default:
        return 'Mar'; // Fallback
    }
  }

  int _getWeekOfYear(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final daysSinceFirstDay = date.difference(firstDayOfYear).inDays;
    return (daysSinceFirstDay / 7).ceil();
  }

  double _calculateScoreUpToDate(DateTime date) {
    final relevantEntries = entries.where((entry) {
      return entry.timestamp.isBefore(date.add(const Duration(days: 1)));
    }).toList();

    if (relevantEntries.isEmpty) return 0.0;

    relevantEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    double score = 0.0;
    final multiplier = _getMultiplierForHabitFrequency();

    for (final entry in relevantEntries) {
      final checkmarkValue = _getCheckmarkValue(entry);
      score = score * multiplier + checkmarkValue * (1 - multiplier);
    }

    return score.clamp(0.0, 1.0);
  }

  int _getCompletionsForPeriod(DateTime startDate, TimeScale timeScale) {
    if (timeScale == TimeScale.week) {
      // For weekly data, use proper calendar week boundaries
      return _getCompletionsForWeek(startDate);
    } else {
      // Fixed logic for Day, Month, Quarter, and Year views
      DateTime periodStart;
      DateTime periodEnd;
      
      switch (timeScale) {
        case TimeScale.day:
          // For day view: count completions only on this specific day
          periodStart = DateTime(startDate.year, startDate.month, startDate.day);
          periodEnd = periodStart.add(const Duration(days: 1));
          break;
          
        case TimeScale.month:
          // For month view: count completions for the entire calendar month
          periodStart = DateTime(startDate.year, startDate.month, 1);
          periodEnd = DateTime(startDate.year, startDate.month + 1, 1);
          break;
          
        case TimeScale.quarter:
          // For quarter view: count completions for the entire calendar quarter
          final quarterStartMonth = ((startDate.month - 1) ~/ 3) * 3 + 1;
          periodStart = DateTime(startDate.year, quarterStartMonth, 1);
          periodEnd = DateTime(startDate.year, quarterStartMonth + 3, 1);
          break;
          
        case TimeScale.year:
          // For year view: count completions for the entire calendar year
          periodStart = DateTime(startDate.year, 1, 1);
          periodEnd = DateTime(startDate.year + 1, 1, 1);
          break;
          
        default:
          // Fallback to original logic
          final endDate = _getNextDateForTimeScale(startDate, timeScale);
          return entries.where((entry) {
            return entry.timestamp.isAfter(startDate.subtract(const Duration(days: 1))) &&
                   entry.timestamp.isBefore(endDate) &&
                   _isEntryCompleted(entry);
          }).length;
      }
      
      // Count completions within the calculated period
      return entries.where((entry) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        return entryDate.isAfter(periodStart.subtract(const Duration(days: 1))) &&
               entryDate.isBefore(periodEnd) &&
               _isEntryCompleted(entry);
      }).length;
    }
  }

  /// Get completions for a specific calendar week using proper week boundaries
  int _getCompletionsForWeek(DateTime weekStartDate) {
    // Calculate the end of the week (6 days after start)
    final weekEndDate = weekStartDate.add(const Duration(days: 6));
    
    // Convert to date-only for accurate comparison
    final startDateOnly = DateTime(weekStartDate.year, weekStartDate.month, weekStartDate.day);
    final endDateOnly = DateTime(weekEndDate.year, weekEndDate.month, weekEndDate.day);
    
    return entries.where((entry) {
      final entryDateOnly = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
      return entryDateOnly.isAfter(startDateOnly.subtract(const Duration(days: 1))) &&
             entryDateOnly.isBefore(endDateOnly.add(const Duration(days: 1))) &&
             _isEntryCompleted(entry);
    }).length;
  }

  /// PHASE 2: Get proper calendar week boundaries based on user preference
  /// Returns a map with 'startDate' and 'endDate' keys
  Future<Map<String, DateTime>> getWeekBoundaries(DateTime date) async {
    final settingsService = SettingsService.instance;
    final startOfWeekDay = await settingsService.getStartOfWeek();
    
    debugPrint('[HABIT_ANALYTICS] Getting week boundaries for $date with start day: ${settingsService.getStartOfWeekDisplayName(startOfWeekDay)}');
    
    // Convert date to date-only (no time component)
    final dateOnly = DateTime(date.year, date.month, date.day);
    
    // Calculate the start of the week
    DateTime startDate;
    if (startOfWeekDay == SettingsService.SUNDAY) {
      // Week starts on Sunday
      final currentWeekday = dateOnly.weekday; // 1=Monday, 7=Sunday
      final daysSinceSunday = currentWeekday == 7 ? 0 : currentWeekday; // Sunday=0, Monday=1, etc.
      startDate = dateOnly.subtract(Duration(days: daysSinceSunday));
    } else {
      // Week starts on Monday
      final currentWeekday = dateOnly.weekday; // 1=Monday, 7=Sunday
      final daysSinceMonday = currentWeekday - 1; // Monday=0, Tuesday=1, etc.
      startDate = dateOnly.subtract(Duration(days: daysSinceMonday));
    }
    
    // End date is 6 days after start date
    final endDate = startDate.add(const Duration(days: 6));
    
    debugPrint('[HABIT_ANALYTICS] Week boundaries calculated: $startDate to $endDate');
    
    return {
      'startDate': startDate,
      'endDate': endDate,
    };
  }

  /// Calculate completion percentage for any time scale with proper date boundaries
  /// Returns (completed_days / total_days_so_far) * 100
  Future<double> calculateCompletionPercentage(TimeScale timeScale) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    DateTime startDate;
    DateTime endDate;
    
    switch (timeScale) {
      case TimeScale.week:
        // Use the existing getWeekBoundaries() helper method for correct start of week
        final weekBoundaries = await getWeekBoundaries(now);
        startDate = weekBoundaries['startDate']!;
        endDate = weekBoundaries['endDate']!;
        break;
      case TimeScale.month:
        // Period from first day of current month to today
        startDate = DateTime(now.year, now.month, 1);
        endDate = DateTime(now.year, now.month + 1, 0); // Last day of current month
        break;
      case TimeScale.year:
        // Period from January 1st of current year to today
        startDate = DateTime(now.year, 1, 1);
        endDate = DateTime(now.year, 12, 31); // Last day of current year
        break;
      default:
        throw ArgumentError('Unsupported time scale: $timeScale');
    }
    
    // Count total days and completed days
    int totalDays = 0;
    int completedDays = 0;
    
    DateTime current = startDate;
    while (current.isBefore(endDate.add(const Duration(days: 1)))) {
      final currentDate = DateTime(current.year, current.month, current.day);
      
      // Only count days up to today (don't include future dates)
      if (currentDate.isAfter(today)) {
        break;
      }
      
      totalDays++;
      
      // Check if habit was completed on this date
      if (habit.isCompletedOnDate(currentDate)) {
        completedDays++;
      }
      
      current = current.add(const Duration(days: 1));
    }
    
    // Calculate percentage with division by zero protection
    final percentage = totalDays > 0 ? (completedDays / totalDays) * 100 : 0.0;
    
    // Mandatory debugging log
    developer.log('DEBUG Percentage | Scale: ${timeScale.name}, Start: $startDate, End: $endDate, Completed: $completedDays, Total: $totalDays, Result: ${percentage.toStringAsFixed(1)}%');
    developer.log('DEBUG Percentage | Habit: ${habit.name}, Entries count: ${entries.length}');
    
    return percentage;
  }

  /// PHASE 2: Calculate "This Week %" using proper calendar week
  Future<double> calculateThisWeekPercentage() async {
    return calculateCompletionPercentage(TimeScale.week);
  }
}

