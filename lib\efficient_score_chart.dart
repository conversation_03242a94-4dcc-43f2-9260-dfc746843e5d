import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;
import 'dart:developer' as developer;
import 'habit_analytics_service.dart';

/// Efficient, on-demand Score Chart implementation based on proven uhabits patterns
/// 
/// Key architectural principles from the reference implementation:
/// 1. On-demand rendering - only creates views for visible dates
/// 2. Stable container layout that fills available space
/// 3. Clean separation between data service and UI
/// 4. Smooth scrolling with efficient viewport management
class EfficientScore<PERSON>hart extends StatefulWidget {
  final HabitAnalyticsService analyticsService;
  final TimeScale timeScale;
  final ScrollController? controller;
  final Color? primaryColor;
  final double height;

  const EfficientScoreChart({
    super.key,
    required this.analyticsService,
    required this.timeScale,
    this.controller,
    this.primaryColor,
    this.height = 200,
  });

  @override
  State<EfficientScoreChart> createState() => _EfficientScoreChartState();
}

class _EfficientScoreChartState extends State<EfficientScoreChart> {
  late ScrollController _scrollController;
  List<ChartDataPoint> _dataPoints = [];
  bool _isLoading = true;
  
  // Chart layout constants - LAYOUT FIX: Minimized padding to fill container
  static const double _paddingTop = 8.0; // Minimal top padding
  static const double _paddingBottom = 8.0; // Minimal bottom padding  
  static const double _paddingLeft = 8.0; // Minimal left padding
  static const double _markerSize = 4.0;
  static const double _strokeWidth = 2.0;
  
  // Dynamic layout calculated based on available space (like reference)
  double _columnWidth = 48.0; // SPACING FIX: Increased from 32.0 to 48.0 for better readability
  int _visibleColumns = 0;
  
  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _loadData();
  }

  @override
  void didUpdateWidget(EfficientScoreChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.timeScale != widget.timeScale || 
        oldWidget.analyticsService != widget.analyticsService) {
      _loadData();
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  /// Load data from analytics service (clean separation of concerns)
  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final data = await widget.analyticsService.getScoreDataForChart(widget.timeScale);
      setState(() {
        _dataPoints = data;
        _isLoading = false;
      });
      
      // Auto-scroll to show recent data after loading
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToRecentData();
      });
      
      developer.log('EfficientScoreChart: Loaded ${data.length} data points for ${widget.timeScale.name}');
    } catch (e) {
      developer.log('EfficientScoreChart: Error loading data: $e');
      setState(() => _isLoading = false);
    }
  }

  /// Scroll to show most recent data (following reference pattern)
  void _scrollToRecentData() {
    if (!_scrollController.hasClients || _dataPoints.isEmpty) return;
    
    try {
      final maxScrollExtent = _scrollController.position.maxScrollExtent;
      if (maxScrollExtent > 0) {
        _scrollController.jumpTo(maxScrollExtent);
      }
    } catch (e) {
      developer.log('EfficientScoreChart: Error scrolling to recent data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = widget.primaryColor ?? theme.colorScheme.primary;
    
    return Container(
      height: widget.height,
      width: double.infinity, // Fill available horizontal space
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _dataPoints.isEmpty
              ? _buildEmptyState(theme)
              : _buildChart(theme, primaryColor),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Text(
        'No data available',
        style: GoogleFonts.inter(
          color: theme.colorScheme.onSurfaceVariant,
          fontSize: 14,
        ),
      ),
    );
  }

  /// Build the main chart with efficient viewport rendering
  Widget _buildChart(ThemeData theme, Color primaryColor) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate dynamic layout like reference implementation
        _calculateDynamicLayout(constraints.maxWidth);
        
        // LAYOUT FIX: Use full height with no padding
        final totalChartWidth = _dataPoints.length * _columnWidth;
        
        return Column(
          children: [
            // LAYOUT FIX: Chart area with minimal Y-axis labels
            Expanded(
              child: Row(
                children: [
                  // Minimal Y-axis labels (30px instead of 60px)
                  SizedBox(
                    width: 30,
                    child: CustomPaint(
                      painter: _YAxisLabelPainter(
                        theme: theme,
                        chartHeight: widget.height - 50,
                        paddingTop: 10,
                      ),
                    ),
                  ),
                  // Chart area
                  Expanded(
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      scrollDirection: Axis.horizontal,
                      child: Container(
                        width: totalChartWidth,
                        height: widget.height - 50, // Reserve 50px for labels (30px + margins)
                        margin: EdgeInsets.only(top: 10, bottom: 10), // Minimal margins
                        child: CustomPaint(
                          painter: _ScoreChartPainter(
                            dataPoints: _dataPoints,
                            primaryColor: primaryColor,
                            theme: theme,
                            columnWidth: _columnWidth,
                            chartHeight: widget.height - 50, // Adjusted height
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // LAYOUT FIX: Use fl_chart's built-in labeling system like History Chart
            Container(
              height: 30, // Reduced height since we're using simpler labeling
              child: Row(
                children: [
                  // Space to align with Y-axis labels
                  SizedBox(width: 30),
                  // X-axis labels using fl_chart system
                  Expanded(
                    child: _buildFlChartXAxisLabels(theme),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build X-axis labels using fl_chart system like History Chart
  Widget _buildFlChartXAxisLabels(ThemeData theme) {
    if (_dataPoints.isEmpty) return Container();
    
    return Container(
      height: 30,
      child: Row(
        children: _dataPoints.asMap().entries.map((entry) {
          final index = entry.key;
          final dataPoint = entry.value;
          
          // Format label based on time scale (same logic as History Chart)
          String label = _formatChartLabel(dataPoint.label, widget.timeScale, index, _dataPoints);
          
          return Container(
            width: _columnWidth,
            child: Center(
              child: Text(
                label,
                style: GoogleFonts.inter(
                  fontSize: 9,
                  color: theme.colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Format chart labels (same logic as History Chart)
  String _formatChartLabel(String originalLabel, TimeScale timeScale, int index, List<ChartDataPoint> dataPoints) {
    final dataPoint = dataPoints[index];
    final date = dataPoint.date;
    
    switch (timeScale) {
      case TimeScale.day:
        // Show day number for day view
        return '${date.day}';
      case TimeScale.week:
        // Show week number for week view
        final weekNumber = _getWeekNumber(date);
        return 'W$weekNumber';
      case TimeScale.month:
        // Show month abbreviation for month view
        return _getMonthAbbreviation(date.month);
      case TimeScale.quarter:
        // Show quarter for quarter view
        return 'Q${_getQuarter(date)}';
      case TimeScale.year:
        // Show year for year view
        return '${date.year}';
    }
  }

  String _getMonthAbbreviation(int month) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[month - 1];
  }

  int _getWeekNumber(DateTime date) {
    final startOfYear = DateTime(date.year, 1, 1);
    final daysSinceStart = date.difference(startOfYear).inDays;
    return (daysSinceStart / 7).floor() + 1;
  }

  int _getQuarter(DateTime date) {
    return ((date.month - 1) / 3).floor() + 1;
  }

  /// Calculate dynamic layout based on available space (like reference onSizeChanged)
  void _calculateDynamicLayout(double availableWidth) {
    if (_dataPoints.isEmpty) return;
    
    // LAYOUT FIX: Account for minimal Y-axis labels (30px)
    final chartAreaWidth = availableWidth - 30;
    
    // Calculate optimal column width based on reference implementation logic
    // Reference: columnWidth = max(columnWidth, maxDayWidth * 1.5f)
    // Reference: nColumns = (width / columnWidth).toInt()
    // Reference: columnWidth = width.toFloat() / nColumns
    
    const minColumnWidth = 40.0; // SPACING FIX: Increased from 24.0 to 40.0 for better spacing
    const maxColumnWidth = 80.0; // SPACING FIX: Increased from 60.0 to 80.0 for better density
    
    // Calculate how many columns can fit comfortably
    _visibleColumns = (chartAreaWidth / minColumnWidth).floor();
    
    // If we have fewer data points than visible columns, use all available space
    if (_dataPoints.length <= _visibleColumns) {
      _columnWidth = chartAreaWidth / _dataPoints.length;
    } else {
      // Use optimal column width for scrolling
      _columnWidth = chartAreaWidth / _visibleColumns;
    }
    
    // Clamp to reasonable bounds
    _columnWidth = _columnWidth.clamp(minColumnWidth, maxColumnWidth);
    
    developer.log('EfficientScoreChart: Layout calculated - availableWidth: $availableWidth, '
        'chartAreaWidth: $chartAreaWidth, columnWidth: $_columnWidth, '
        'visibleColumns: $_visibleColumns, dataPoints: ${_dataPoints.length}');
  }
}

/// Custom painter for the score chart line and markers
/// Implements efficient rendering similar to the reference implementation
class _ScoreChartPainter extends CustomPainter {
  final List<ChartDataPoint> dataPoints;
  final Color primaryColor;
  final ThemeData theme;
  final double columnWidth;
  final double chartHeight;

  _ScoreChartPainter({
    required this.dataPoints,
    required this.primaryColor,
    required this.theme,
    required this.columnWidth,
    required this.chartHeight,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.isEmpty) return;

    final paint = Paint()
      ..color = primaryColor
      ..strokeWidth = _EfficientScoreChartState._strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final markerPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    final gridPaint = Paint()
      ..color = theme.colorScheme.outline.withOpacity(0.2)
      ..strokeWidth = 1.0;

    // Draw grid lines (horizontal)
    _drawGrid(canvas, size, gridPaint);

    // Draw the score line and markers
    _drawScoreLine(canvas, size, paint, markerPaint);
  }

  void _drawGrid(Canvas canvas, Size size, Paint gridPaint) {
    // Draw horizontal grid lines matching Y-axis labels: 0%, 20%, 40%, 60%, 80%, 100%
    // Based on reference implementation: 5 rows
    const nRows = 5;
    final rowHeight = chartHeight / nRows;
    
    for (int i = 0; i <= nRows; i++) {
      final y = i * rowHeight;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
  }

  void _drawScoreLine(Canvas canvas, Size size, Paint linePaint, Paint markerPaint) {
    final path = Path();
    final points = <Offset>[];

    // Calculate points with proper grid alignment
    // Grid has 5 rows: 100%, 80%, 60%, 40%, 20%, 0%
    const nRows = 5;
    final rowHeight = chartHeight / nRows;

    for (int i = 0; i < dataPoints.length; i++) {
      // Center data points in their columns (like reference implementation)
      final x = i * columnWidth + columnWidth / 2;
      
      // Convert data value (0.0-1.0) to Y coordinate with proper grid alignment
      // Reference maps score to height: height = (columnHeight * score).toInt()
      // Then positions from bottom: internalPaddingTop + columnHeight - height
      final normalizedValue = dataPoints[i].value.clamp(0.0, 1.0);
      final y = chartHeight * (1.0 - normalizedValue);
      
      points.add(Offset(x, y));
    }

    // Draw line connecting points
    if (points.length > 1) {
      path.moveTo(points[0].dx, points[0].dy);
      for (int i = 1; i < points.length; i++) {
        path.lineTo(points[i].dx, points[i].dy);
      }
      canvas.drawPath(path, linePaint);
    }

    // Draw markers at each point (similar to reference implementation)
    for (int i = 0; i < points.length; i++) {
      final point = points[i];
      final dataPoint = dataPoints[i];
      
      // Draw marker with background (like reference drawMarker method)
      final markerRadius = _EfficientScoreChartState._markerSize;
      
      // Background circle (white/surface color)
      final backgroundPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(point, markerRadius + 1, backgroundPaint);
      
      // Main marker circle
      final markerColor = dataPoint.isFuture 
          ? markerPaint.color.withOpacity(0.5)
          : markerPaint.color;
      
      final actualMarkerPaint = Paint()
        ..color = markerColor
        ..style = PaintingStyle.fill;
        
      canvas.drawCircle(point, markerRadius, actualMarkerPaint);
    }
  }

  @override
  bool shouldRepaint(covariant _ScoreChartPainter oldDelegate) {
    return oldDelegate.dataPoints != dataPoints ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.columnWidth != columnWidth ||
           oldDelegate.chartHeight != chartHeight;
  }
}

/// Custom painter for Y-axis percentage labels (sticky, non-scrolling)
class _YAxisLabelPainter extends CustomPainter {
  final ThemeData theme;
  final double chartHeight;
  final double paddingTop;

  _YAxisLabelPainter({
    required this.theme,
    required this.chartHeight,
    required this.paddingTop,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    final textStyle = GoogleFonts.inter(
      fontSize: 11,
      color: theme.colorScheme.onSurfaceVariant,
      fontWeight: FontWeight.w500,
    );

    // Draw percentage labels: 100%, 80%, 60%, 40%, 20%, 0%
    // CRITICAL: Must match exact grid line positions for perfect alignment
    const nRows = 5;
    final rowHeight = chartHeight / nRows;

    for (int i = 0; i <= nRows; i++) {
      final percentage = 100 - (i * 100 / nRows);
      
      // ALIGNMENT FIX: Calculate exact Y position to match grid lines
      // Grid lines are drawn at: paddingTop + (i * rowHeight)
      // Labels must be positioned at exactly the same Y coordinate
      final gridLineY = paddingTop + (i * rowHeight);
      
      textPainter.text = TextSpan(
        text: '${percentage.toInt()}%',
        style: textStyle,
      );
      textPainter.layout();
      
      final offset = Offset(
        size.width - 8, // Right-aligned with small padding
        gridLineY - textPainter.height / 2, // Center text on exact grid line position
      );
      
      textPainter.paint(canvas, offset);
    }
  }

  @override
  bool shouldRepaint(covariant _YAxisLabelPainter oldDelegate) {
    return oldDelegate.chartHeight != chartHeight ||
           oldDelegate.paddingTop != paddingTop;
  }
}

// X-axis labels now handled by _buildFlChartXAxisLabels method using simple widget approach