// debugPrint requires flutter/foundation.dart import
// This provides throttled printing that won't drop messages in production
import 'package:flutter/foundation.dart';

import 'entry.dart';
import 'habit_analytics.dart';

/// Enum to define the type of habit
enum HabitType {
  boolean,
  numerical,
}

class Habit {
  // IMPORT_CHECK: Verify debugPrint is available
  static void _verifyImports() {
    debugPrint('[IMPORT_CHECK] Habit.dart loaded successfully with debugPrint available');
  }

  final String id;
  final String name;
  final List<String> sectionIds; // MULTI-SECTION TAGGING: Habits can belong to multiple sections
  final int orderIndex; // ORDER PERSISTENCE: Explicit ordering for database persistence
  final HabitType type; // NEW: Type of habit (boolean or numerical)
  final double? targetValue; // NEW: Target value for numerical habits
  final String? unit; // NEW: Unit for numerical habits (e.g., "glasses", "minutes")
  final DateTime createdAt; // NEW: Creation timestamp for analytics
  
  // LEGACY: Keep for backward compatibility during migration
  Map<DateTime, bool> completions;
  
  // NEW: Enhanced entry system
  List<Entry> entries;

  Habit({
    String? id,
    required this.name,
    List<String>? sectionIds,
    this.orderIndex = 0, // Default order index
    this.type = HabitType.boolean, // Default to boolean for backward compatibility
    this.targetValue,
    this.unit,
    DateTime? createdAt,
    Map<DateTime, bool>? completions,
    List<Entry>? entries,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString(),
       sectionIds = sectionIds ?? [],
       createdAt = createdAt ?? DateTime.now(),
       completions = completions ?? {},
       entries = entries ?? [] {
    // Verify imports on construction
    _verifyImports();
  }

  // Helper method to check if habit is completed on a specific date
  bool isCompletedOnDate(DateTime date) {
    final dateKey = DateTime(date.year, date.month, date.day);
    
    // Check new entry system first
    final entry = getEntryForDate(date);
    if (entry != null) {
      if (type == HabitType.boolean) {
        return entry.boolValue;
      } else {
        // For numerical habits, consider completed if >= 80% of target
        final numValue = entry.numericalValue;
        final result = targetValue != null && numValue != null && numValue >= (targetValue! * 0.8);
        debugPrint('[HABIT] Numerical habit completion check - target: $targetValue, value: $numValue, result: $result');
        return result;
      }
    }
    
    // Fallback to legacy system
    return completions[dateKey] ?? false;
  }

  // Helper method to toggle completion status for a specific date
  void toggleCompletionForDate(DateTime date) {
    final dateKey = DateTime(date.year, date.month, date.day);
    
    if (type == HabitType.boolean) {
      final currentEntry = getEntryForDate(date);
      final newValue = !(currentEntry?.boolValue ?? false);
      addEntry(Entry(
        habitId: id,
        timestamp: date,
        value: newValue,
        type: EntryType.boolean,
      ));
    }
    
    // Also update legacy system for backward compatibility
    completions[dateKey] = !(completions[dateKey] ?? false);
  }

  // Helper method to set completion status for a specific date
  void setCompletionForDate(DateTime date, bool isCompleted) {
    final dateKey = DateTime(date.year, date.month, date.day);
    
    if (type == HabitType.boolean) {
      addEntry(Entry(
        habitId: id,
        timestamp: date,
        value: isCompleted,
        type: EntryType.boolean,
      ));
    }
    
    // Also update legacy system for backward compatibility
    completions[dateKey] = isCompleted;
  }

  // NEW: Add an entry for this habit
  void addEntry(Entry entry) {
    // Remove any existing entry for the same date
    final date = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
    entries.removeWhere((e) => 
      DateTime(e.timestamp.year, e.timestamp.month, e.timestamp.day) == date);
    
    // Add the new entry
    entries.add(entry);
    entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  // NEW: Get entry for a specific date
  Entry? getEntryForDate(DateTime date) {
    final dateKey = DateTime(date.year, date.month, date.day);
    final matchingEntries = entries.where((entry) {
      final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
      return entryDate == dateKey;
    }).toList();
    
    return matchingEntries.isNotEmpty ? matchingEntries.last : null;
  }

  // NEW: Set numerical value for a specific date
  void setNumericalValueForDate(DateTime date, double value, {String? note}) {
    if (type != HabitType.numerical) {
      throw StateError('Cannot set numerical value for non-numerical habit');
    }
    
    addEntry(Entry(
      habitId: id,
      timestamp: date,
      value: value,
      note: note,
      type: EntryType.numerical,
    ));
  }

  // NEW: Get numerical value for a specific date
  double? getNumericalValueForDate(DateTime date) {
    final entry = getEntryForDate(date);
    return entry?.type == EntryType.numerical ? entry?.numericalValue : null;
  }

  // NEW: Analytics methods
  double get habitScore7Day {
    return HabitAnalytics.calculateHabitScore(
      entries: entries,
      targetValue: DateTime.fromMillisecondsSinceEpoch((targetValue ?? 1.0).toInt()),
      windowDays: 7,
    );
  }

  double get habitScore30Day {
    return HabitAnalytics.calculateHabitScore(
      entries: entries,
      targetValue: DateTime.fromMillisecondsSinceEpoch((targetValue ?? 1.0).toInt()),
      windowDays: 30,
    );
  }

  int get currentStreak {
    return HabitAnalytics.calculateCurrentStreak(entries);
  }

  int get bestStreak {
    return HabitAnalytics.calculateBestStreak(entries);
  }

  int get totalCompletions {
    return HabitAnalytics.calculateTotalCompletions(entries);
  }

  // MULTI-SECTION TAGGING: Helper methods for section management
  bool belongsToSection(String sectionId) {
    return sectionIds.contains(sectionId);
  }

  void addToSection(String sectionId) {
    if (!sectionIds.contains(sectionId)) {
      sectionIds.add(sectionId);
    }
  }

  void removeFromSection(String sectionId) {
    sectionIds.remove(sectionId);
  }

  // Serialization methods for database persistence
  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'sectionIds': sectionIds, // MULTI-SECTION TAGGING: Store section IDs list
    'orderIndex': orderIndex, // ORDER PERSISTENCE: Store explicit order
    'type': type.name, // NEW: Store habit type
    'targetValue': targetValue, // NEW: Store target value for numerical habits
    'unit': unit, // NEW: Store unit for numerical habits
    'createdAt': createdAt.toIso8601String(), // NEW: Store creation timestamp
    // Convert DateTime keys to ISO 8601 strings for storage
    'completions': completions.map((key, value) => MapEntry(key.toIso8601String(), value)),
    'entries': entries.map((entry) => entry.toJson()).toList(), // NEW: Store entries
  };

  static Habit fromJson(Map<String, dynamic> json) {
    // Parse habit type
    final typeString = json['type'] as String?;
    final habitType = typeString != null 
        ? HabitType.values.firstWhere((e) => e.name == typeString, orElse: () => HabitType.boolean)
        : HabitType.boolean;

    // Parse entries
    final entriesJson = json['entries'] as List<dynamic>? ?? [];
    final entries = entriesJson.map((entryJson) => Entry.fromJson(entryJson as Map<String, dynamic>)).toList();

    // Parse createdAt with fallback for existing habits
    DateTime createdAt;
    final createdAtString = json['createdAt'] as String?;
    if (createdAtString != null) {
      createdAt = DateTime.parse(createdAtString);
    } else {
      // Fallback for existing habits without createdAt
      createdAt = DateTime(2024, 1, 1); // Default to a sensible past date
    }

    return Habit(
      id: json['id'],
      name: json['name'],
      sectionIds: List<String>.from(json['sectionIds'] ?? []), // MULTI-SECTION TAGGING: Load section IDs
      orderIndex: json['orderIndex'] ?? 0, // ORDER PERSISTENCE: Load order index with fallback
      type: habitType, // NEW: Load habit type
      targetValue: json['targetValue']?.toDouble(), // NEW: Load target value
      unit: json['unit'], // NEW: Load unit
      createdAt: createdAt, // NEW: Load creation timestamp with fallback
      completions: (json['completions'] as Map<String, dynamic>? ?? {}).map(
        (key, value) => MapEntry(DateTime.parse(key), value as bool),
      ),
      entries: entries, // NEW: Load entries
    );
  }

  // Create a copy of this habit with updated properties
  Habit copyWith({
    String? name,
    List<String>? sectionIds,
    int? orderIndex,
    HabitType? type,
    double? targetValue,
    String? unit,
    DateTime? createdAt,
    Map<DateTime, bool>? completions,
    List<Entry>? entries,
  }) {
    return Habit(
      id: id,
      name: name ?? this.name,
      sectionIds: sectionIds ?? List.from(this.sectionIds), // MULTI-SECTION TAGGING: Copy section IDs
      orderIndex: orderIndex ?? this.orderIndex, // ORDER PERSISTENCE: Copy order index
      type: type ?? this.type, // NEW: Copy habit type
      targetValue: targetValue ?? this.targetValue, // NEW: Copy target value
      unit: unit ?? this.unit, // NEW: Copy unit
      createdAt: createdAt ?? this.createdAt, // NEW: Copy creation timestamp
      completions: completions ?? Map.from(this.completions),
      entries: entries ?? List.from(this.entries), // NEW: Copy entries
    );
  }
}
