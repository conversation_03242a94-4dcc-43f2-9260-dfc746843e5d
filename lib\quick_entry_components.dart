import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'habit.dart';
import 'entry.dart';
import 'database_service.dart';

/// Quick entry dialog for numerical habits with +/- buttons
class QuickNumericalEntryDialog extends StatefulWidget {
  final Habit habit;
  final DateTime date;
  final Entry? existingEntry;

  const QuickNumericalEntryDialog({
    super.key,
    required this.habit,
    required this.date,
    this.existingEntry,
  });

  @override
  State<QuickNumericalEntryDialog> createState() => _QuickNumericalEntryDialogState();
}

class _QuickNumericalEntryDialogState extends State<QuickNumericalEntryDialog>
    with SingleTickerProviderStateMixin {
  final _databaseService = DatabaseService();
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  double _currentValue = 0.0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.existingEntry?.numericalValue ?? 0.0;
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _adjustValue(double delta) {
    setState(() {
      _currentValue = (_currentValue + delta).clamp(0.0, double.infinity);
    });
    
    // Haptic feedback
    HapticFeedback.lightImpact();
  }

  Future<void> _saveEntry() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final entry = Entry(
        id: widget.existingEntry?.id,
        habitId: widget.habit.id,
        timestamp: widget.date,
        value: _currentValue,
        type: EntryType.numerical,
      );

      // COMPREHENSIVE DEBUGGING: Save entry and habit
      debugPrint('[QUICK_NUMERICAL_ENTRY] Saving entry and updating habit');
      debugPrint('[QUICK_NUMERICAL_ENTRY] Habit: ${widget.habit.name} (ID: ${widget.habit.id})');
      debugPrint('[QUICK_NUMERICAL_ENTRY] Entry: ${entry.toJson()}');
      await _databaseService.saveEntry(entry);
      widget.habit.addEntry(entry);
      await _databaseService.saveHabit(widget.habit);
      debugPrint('[QUICK_NUMERICAL_ENTRY] Entry and habit saved successfully');

      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final targetValue = widget.habit.targetValue ?? 1.0;
    final progress = _currentValue / targetValue;
    final unit = widget.habit.unit ?? '';

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 320,
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  Text(
                    widget.habit.name,
                    style: GoogleFonts.inter(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatDate(widget.date),
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Progress indicator
                  Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.outline.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: progress.clamp(0.0, 1.0),
                      child: Container(
                        decoration: BoxDecoration(
                          color: _getProgressColor(progress),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Current value display
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      textBaseline: TextBaseline.alphabetic,
                      children: [
                        Text(
                          _currentValue.toStringAsFixed(_currentValue % 1 == 0 ? 0 : 1),
                          style: GoogleFonts.inter(
                            fontSize: 36,
                            fontWeight: FontWeight.w700,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                        if (unit.isNotEmpty) ...[
                          const SizedBox(width: 8),
                          Text(
                            unit,
                            style: GoogleFonts.inter(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: theme.colorScheme.onSurface.withOpacity(0.6),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Target display
                  Text(
                    'Target: ${targetValue.toStringAsFixed(targetValue % 1 == 0 ? 0 : 1)} $unit',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: theme.colorScheme.onSurface.withOpacity(0.5),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Control buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildControlButton(
                        icon: Icons.remove,
                        onPressed: () => _adjustValue(-1),
                        onLongPress: () => _adjustValue(-5),
                      ),
                      _buildControlButton(
                        icon: Icons.remove,
                        label: '0.1',
                        onPressed: () => _adjustValue(-0.1),
                        size: 40,
                      ),
                      _buildControlButton(
                        icon: Icons.add,
                        label: '0.1',
                        onPressed: () => _adjustValue(0.1),
                        size: 40,
                      ),
                      _buildControlButton(
                        icon: Icons.add,
                        onPressed: () => _adjustValue(1),
                        onLongPress: () => _adjustValue(5),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                          child: Text(
                            'Cancel',
                            style: GoogleFonts.inter(fontWeight: FontWeight.w500),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _saveEntry,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : Text(
                                  'Save',
                                  style: GoogleFonts.inter(fontWeight: FontWeight.w600),
                                ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    VoidCallback? onLongPress,
    String? label,
    double size = 56,
  }) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onPressed,
      onLongPress: onLongPress,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(size / 2),
          border: Border.all(
            color: theme.colorScheme.primary.withOpacity(0.3),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: theme.colorScheme.primary,
              size: size * 0.4,
            ),
            if (label != null)
              Text(
                label,
                style: GoogleFonts.inter(
                  fontSize: 8,
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.primary,
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final entryDate = DateTime(date.year, date.month, date.day);
    
    if (entryDate == today) {
      return 'Today';
    } else if (entryDate == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Color _getProgressColor(double progress) {
    if (progress >= 1.0) return Colors.green;
    if (progress >= 0.8) return Colors.blue;
    if (progress >= 0.5) return Colors.orange;
    return Colors.red;
  }
}

/// Batch entry dialog for entering multiple dates at once
class BatchEntryDialog extends StatefulWidget {
  final Habit habit;
  final List<DateTime> selectedDates;

  const BatchEntryDialog({
    super.key,
    required this.habit,
    required this.selectedDates,
  });

  @override
  State<BatchEntryDialog> createState() => _BatchEntryDialogState();
}

class _BatchEntryDialogState extends State<BatchEntryDialog> {
  final _databaseService = DatabaseService();
  final _noteController = TextEditingController();
  final _valueController = TextEditingController();
  
  bool _booleanValue = true;
  double _numericalValue = 0.0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.habit.type == HabitType.numerical && widget.habit.targetValue != null) {
      _numericalValue = widget.habit.targetValue!;
      _valueController.text = _numericalValue.toString();
    }
  }

  @override
  void dispose() {
    _noteController.dispose();
    _valueController.dispose();
    super.dispose();
  }

  Future<void> _saveBatchEntries() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final entries = <Entry>[];
      final note = _noteController.text.trim().isEmpty ? null : _noteController.text.trim();

      for (final date in widget.selectedDates) {
        final entry = Entry(
          habitId: widget.habit.id,
          timestamp: date,
          value: widget.habit.type == HabitType.boolean ? _booleanValue : _numericalValue,
          note: note,
          type: widget.habit.type == HabitType.boolean ? EntryType.boolean : EntryType.numerical,
        );
        entries.add(entry);
      }

      // Save all entries
      for (final entry in entries) {
        await _databaseService.saveEntry(entry);
        widget.habit.addEntry(entry);
      }

      // COMPREHENSIVE DEBUGGING: Save habit after bulk entry addition
      debugPrint('[QUICK_BULK_ENTRY] Saving habit after adding ${entries.length} entries');
      debugPrint('[QUICK_BULK_ENTRY] Habit: ${widget.habit.name} (ID: ${widget.habit.id})');
      debugPrint('[QUICK_BULK_ENTRY] Entries added: ${entries.map((e) => e.toJson()).toList()}');
      await _databaseService.saveHabit(widget.habit);
      debugPrint('[QUICK_BULK_ENTRY] Habit saved successfully after bulk addition');

      if (mounted) {
        Navigator.of(context).pop(true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Added ${entries.length} entries'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save entries: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Batch Entry',
              style: GoogleFonts.inter(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${widget.habit.name} • ${widget.selectedDates.length} dates',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            const SizedBox(height: 24),

            // Selected dates preview
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Selected Dates:',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Wrap(
                    spacing: 4,
                    runSpacing: 4,
                    children: widget.selectedDates.take(10).map((date) {
                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          '${date.day}/${date.month}',
                          style: GoogleFonts.inter(
                            fontSize: 10,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                  if (widget.selectedDates.length > 10)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        '+${widget.selectedDates.length - 10} more',
                        style: GoogleFonts.inter(
                          fontSize: 10,
                          color: theme.colorScheme.onSurface.withOpacity(0.5),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Value input
            if (widget.habit.type == HabitType.boolean) ...[
              Row(
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Mark as completed',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Switch(
                    value: _booleanValue,
                    onChanged: (value) {
                      setState(() {
                        _booleanValue = value;
                      });
                    },
                  ),
                ],
              ),
            ] else ...[
              TextField(
                controller: _valueController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                onChanged: (value) {
                  _numericalValue = double.tryParse(value) ?? 0.0;
                },
                decoration: InputDecoration(
                  labelText: 'Value',
                  suffixText: widget.habit.unit,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
            const SizedBox(height: 16),

            // Note field
            TextField(
              controller: _noteController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: 'Note (optional)',
                hintText: 'Add a note for all entries...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Actions
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveBatchEntries,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Save All'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Habit type indicator widget
class HabitTypeIndicator extends StatelessWidget {
  final HabitType type;
  final String? unit;
  final double size;

  const HabitTypeIndicator({
    super.key,
    required this.type,
    this.unit,
    this.size = 20,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Tooltip(
      message: _getTooltipMessage(),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: _getTypeColor().withOpacity(0.1),
          borderRadius: BorderRadius.circular(size / 4),
        ),
        child: Icon(
          _getTypeIcon(),
          size: size * 0.6,
          color: _getTypeColor(),
        ),
      ),
    );
  }

  IconData _getTypeIcon() {
    switch (type) {
      case HabitType.boolean:
        return Icons.check;
      case HabitType.numerical:
        return Icons.numbers;
    }
  }

  Color _getTypeColor() {
    switch (type) {
      case HabitType.boolean:
        return Colors.green;
      case HabitType.numerical:
        return Colors.blue;
    }
  }

  String _getTooltipMessage() {
    switch (type) {
      case HabitType.boolean:
        return 'Yes/No habit';
      case HabitType.numerical:
        return 'Numerical habit${unit != null ? ' ($unit)' : ''}';
    }
  }
}