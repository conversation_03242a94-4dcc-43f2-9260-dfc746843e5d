import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'modern_theme.dart';

class ModernCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final Color? color;
  final VoidCallback? onTap;

  const ModernCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation,
    this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final card = Card(
      elevation: elevation ?? 2,
      color: color,
      margin: margin ?? const EdgeInsets.all(8),
      child: Padding(
        padding: padding ?? EdgeInsets.all(ModernTheme.spaceMD), // 25% reduction: 12px from 16px
        child: child,
      ),
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: card,
      );
    }

    return card;
  }
}

class ModernSectionSelector extends StatelessWidget {
  final String? selectedSectionId;
  final List<dynamic> sections;
  final ValueChanged<String?> onChanged;

  const ModernSectionSelector({
    super.key,
    required this.selectedSectionId,
    required this.sections,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: ModernTheme.spaceMD, vertical: ModernTheme.spaceSM), // 25% reduction: 12px horizontal, 8px vertical
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String?>(
          value: selectedSectionId,
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: theme.colorScheme.primary,
            size: 20,
          ),
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.primary,
          ),
          dropdownColor: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          onChanged: onChanged,
          items: [
            DropdownMenuItem(
              value: null,
              child: Text(
                "All Habits",
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
            ...sections.map<DropdownMenuItem<String>>((section) {
              return DropdownMenuItem<String>(
                value: section.id,
                child: Text(
                  section.name,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }
}

class ModernIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final String tooltip;
  final Color? color;
  final double size;

  const ModernIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    required this.tooltip,
    this.color,
    this.size = 24,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.light
                ? ModernTheme.lightCardShadow
                : ModernTheme.darkCardShadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(icon, size: size),
        onPressed: onPressed,
        tooltip: tooltip,
        color: color ?? Theme.of(context).colorScheme.onSurface,
      ),
    );
  }
}

class ModernProgressIndicator extends StatelessWidget {
  final double percentage;
  final double size;
  final Color? color;

  const ModernProgressIndicator({
    super.key,
    required this.percentage,
    this.size = 60,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = color ?? theme.colorScheme.primary;

    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        children: [
          CircularProgressIndicator(
            value: percentage / 100,
            backgroundColor: theme.colorScheme.surfaceContainerHighest,
            valueColor: AlwaysStoppedAnimation<Color>(progressColor),
            strokeWidth: 4,
          ),
          Center(
            child: Text(
              '${percentage.round()}%',
              style: GoogleFonts.inter(
                fontSize: size * 0.2,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ModernDateChip extends StatelessWidget {
  final DateTime date;
  final bool isSelected;
  final bool isToday;
  final double completionPercentage;
  final VoidCallback onTap;

  const ModernDateChip({
    super.key,
    required this.date,
    required this.isSelected,
    required this.isToday,
    required this.completionPercentage,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final dayOfWeek = _getDayOfWeekAbbreviation(date.weekday);

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: EdgeInsets.symmetric(horizontal: ModernTheme.spaceSM * 1.1, vertical: ModernTheme.spaceSM), // 25% reduction: 9px horizontal, 8px vertical
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primary
              : isToday
                  ? theme.colorScheme.primary.withOpacity(0.1)
                  : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: isToday && !isSelected
              ? Border.all(color: theme.colorScheme.primary, width: 2)
              : null,
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: theme.colorScheme.primary.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : [
                  BoxShadow(
                    color: theme.brightness == Brightness.light
                        ? ModernTheme.lightCardShadow
                        : ModernTheme.darkCardShadow,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${completionPercentage.round()}%',
              style: GoogleFonts.inter(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: isSelected
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              dayOfWeek,
              style: GoogleFonts.inter(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: isSelected
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              date.day.toString(),
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isSelected
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getDayOfWeekAbbreviation(int weekday) {
    switch (weekday) {
      case 1: return 'MO';
      case 2: return 'TU';
      case 3: return 'WE';
      case 4: return 'TH';
      case 5: return 'FR';
      case 6: return 'SA';
      case 7: return 'SU';
      default: return '';
    }
  }
}

class ModernHabitCompletionIndicator extends StatefulWidget {
  final bool isCompleted;
  final VoidCallback onTap;
  final bool isToday;

  const ModernHabitCompletionIndicator({
    super.key,
    required this.isCompleted,
    required this.onTap,
    this.isToday = false,
  });

  @override
  State<ModernHabitCompletionIndicator> createState() => _ModernHabitCompletionIndicatorState();
}

class _ModernHabitCompletionIndicatorState extends State<ModernHabitCompletionIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final successColor = theme.brightness == Brightness.light
        ? ModernTheme.lightSuccess
        : ModernTheme.darkSuccess;

    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) {
        _animationController.reverse();
        widget.onTap();
      },
      onTapCancel: () => _animationController.reverse(),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: widget.isCompleted
                    ? successColor
                    : Colors.transparent,
                border: Border.all(
                  color: widget.isCompleted
                      ? successColor
                      : widget.isToday
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
                  width: 2,
                ),
                boxShadow: widget.isCompleted
                    ? [
                        BoxShadow(
                          color: successColor.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: widget.isCompleted
                  ? Icon(
                      Icons.check,
                      size: 20,
                      color: theme.colorScheme.onPrimary,
                    )
                  : null,
            ),
          );
        },
      ),
    );
  }
}