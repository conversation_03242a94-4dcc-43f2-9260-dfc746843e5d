// Helper model for TableView row data mapping
import 'habit.dart';
import 'section.dart';

/// Sealed class representing different types of rows in the TableView
sealed class TableRowData {}

/// Represents a section header row in the table
class SectionHeaderRow extends TableRowData {
  final Section section;
  
  SectionHeaderRow(this.section);
  
  @override
  String toString() => 'SectionHeaderRow(${section.name})';
}

/// Represents a habit data row in the table
class HabitDataRow extends TableRowData {
  final Habit habit;
  final Section parentSection;
  
  HabitDataRow(this.habit, this.parentSection);
  
  @override
  String toString() => 'HabitDataRow(${habit.name} in ${parentSection.name})';
}