// MULTI-SECTION TAGGING: Simplified Section class - no longer contains habits
class Section {
  final String id;
  final String name;
  final String color; // NEW: Hex color string for section identification
  final int orderIndex; // Order of sections themselves
  final List<String> habitOrder; // Order of habits within this section

  Section({
    String? id,
    required this.name,
    String? color,
    this.orderIndex = 0,
    List<String>? habitOrder,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString(),
       color = color ?? '#3B82F6', // Default to blue if no color specified
       habitOrder = habitOrder ?? [];

  // MULTI-SECTION TAGGING: Helper methods for working with habits
  // Note: Habits are now stored separately and reference sections by ID

  // Serialization methods for database persistence
  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'color': color,
    'orderIndex': orderIndex,
    'habitOrder': habitOrder,
  };

  static Section from<PERSON>son(Map<String, dynamic> json) {
    return Section(
      id: json['id'],
      name: json['name'],
      color: json['color'] ?? '#3B82F6', // Backward compatibility with existing sections
      orderIndex: json['orderIndex'] ?? 0,
      habitOrder: List<String>.from(json['habitOrder'] ?? []),
    );
  }

  // Create a copy of this section with updated properties
  Section copyWith({
    String? name,
    String? color,
    int? orderIndex,
    List<String>? habitOrder,
  }) {
    return Section(
      id: id,
      name: name ?? this.name,
      color: color ?? this.color,
      orderIndex: orderIndex ?? this.orderIndex,
      habitOrder: habitOrder ?? List.from(this.habitOrder),
    );
  }

  @override
  String toString() {
    return 'Section(id: $id, name: $name, orderIndex: $orderIndex, habitOrder: $habitOrder)';
  }
}