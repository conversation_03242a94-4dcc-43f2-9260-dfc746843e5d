import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:developer' as developer;
import 'habit_analytics_service.dart';

/// Foundation Score Chart - Rebuilt with Perfect Alignment
/// 
/// Features:
/// 1. Perfect Y-axis and chart data alignment
/// 2. Proper spacing for all labels (top and bottom)
/// 3. Clean coordinate system
/// 4. Interactive value labels
/// 5. Horizontal scrolling with current period positioning
class FoundationScoreChart extends StatefulWidget {
  final HabitAnalyticsService analyticsService;
  final TimeScale timeScale;
  final ScrollController? controller;
  final double height;

  const FoundationScoreChart({
    super.key,
    required this.analyticsService,
    required this.timeScale,
    this.controller,
    this.height = 240,
  });

  @override
  State<FoundationScoreChart> createState() => _FoundationScoreChartState();
}

class _FoundationScoreChartState extends State<FoundationScoreChart> {
  late ScrollController _scrollController;
  List<ChartDataPoint> _allDataPoints = [];
  bool _isLoading = true;
  
  // Layout constants - REBUILT
  static const double _yAxisWidth = 50.0;
  static const double _topPadding = 60.0;    // Space for 100% labels
  static const double _bottomPadding = 50.0; // Space for X-axis labels
  static const int _visibleDataPoints = 10; // REFINEMENT: Further reduced from 12 to 10 for optimal spacing
  
  // Dynamic layout variables - REBUILT
  double _columnWidth = 80.0; // SPACING FIX: Increased from 60.0 to 80.0 for better readability
  double _chartAreaHeight = 0.0;
  double _baselineY = 0.0;
  
  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void didUpdateWidget(FoundationScoreChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.timeScale != widget.timeScale) {
      developer.log('FoundationScoreChart: Time scale changed from ${oldWidget.timeScale.name} to ${widget.timeScale.name}');
      _loadInitialData();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // BUGFIX: Also try to scroll when dependencies change (layout complete)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scheduleScrollToRecentData();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  /// Load initial data - enough to fill viewport plus buffer for scrolling
  Future<void> _loadInitialData() async {
    setState(() => _isLoading = true);
    
    try {
      developer.log('FoundationScoreChart: Loading initial data for ${widget.timeScale.name}');
      
      // BUGFIX: Use analytics service instead of internal data generation
      developer.log('FoundationScoreChart: Calling analytics service for ${widget.timeScale.name}');
      final points = await widget.analyticsService.getScoreDataForChart(widget.timeScale);
      developer.log('FoundationScoreChart: Received ${points.length} points from analytics service');
      
      setState(() {
        _allDataPoints = points;
        _isLoading = false;
      });
      
      // BUGFIX: Scroll to show recent data with proper positioning and multiple attempts
      _scheduleScrollToRecentData();
      
      developer.log('FoundationScoreChart: Loaded ${points.length} initial data points');
    } catch (e) {
      developer.log('FoundationScoreChart: Error loading initial data: $e');
      setState(() => _isLoading = false);
    }
  }

  /// Generate data points with correct +2 period offset
  Future<List<ChartDataPoint>> _generateDataPoints(int count) async {
    final now = DateTime.now();
    final points = <ChartDataPoint>[];
    
    // Calculate the end date: current period + 2 periods
    final endDate = _getNextDate(_getNextDate(_getCurrentPeriodStart(now)));
    
    // Start from end date and go backwards to generate the timeline
    DateTime current = endDate;
    for (int i = 0; i < count; i++) {
      // Get actual score data for this date
      final score = await _getScoreForDate(current);
      
      points.insert(0, ChartDataPoint(
        date: current,
        value: score,
        label: _formatDateLabel(current),
        isFuture: current.isAfter(now),
      ));
      
      current = _getPreviousDate(current);
    }
    
    developer.log('FoundationScoreChart: Generated timeline from ${points.first.date} to ${points.last.date} (end: $endDate)');
    return points;
  }

  /// Get the start of the current period based on time scale
  DateTime _getCurrentPeriodStart(DateTime date) {
    switch (widget.timeScale) {
      case TimeScale.day:
        return DateTime(date.year, date.month, date.day);
      case TimeScale.week:
        // Get start of current week (Monday)
        final daysFromMonday = date.weekday - 1;
        return DateTime(date.year, date.month, date.day - daysFromMonday);
      case TimeScale.month:
        return DateTime(date.year, date.month, 1);
      case TimeScale.quarter:
        // Get start of current quarter
        final quarterStartMonth = ((date.month - 1) ~/ 3) * 3 + 1;
        return DateTime(date.year, quarterStartMonth, 1);
      case TimeScale.year:
        return DateTime(date.year, 1, 1);
    }
  }

  /// Get next date based on time scale
  DateTime _getNextDate(DateTime date) {
    switch (widget.timeScale) {
      case TimeScale.day:
        return date.add(Duration(days: 1));
      case TimeScale.week:
        return date.add(Duration(days: 7));
      case TimeScale.month:
        int nextMonth = date.month + 1;
        int nextYear = date.year;
        if (nextMonth > 12) {
          nextMonth = 1;
          nextYear += 1;
        }
        return DateTime(nextYear, nextMonth, date.day.clamp(1, 28));
      case TimeScale.quarter:
        int nextMonth = date.month + 3;
        int nextYear = date.year;
        while (nextMonth > 12) {
          nextMonth -= 12;
          nextYear += 1;
        }
        return DateTime(nextYear, nextMonth, date.day.clamp(1, 28));
      case TimeScale.year:
        return DateTime(date.year + 1, date.month, date.day.clamp(1, 28));
    }
  }

  /// Get score data for a specific date
  Future<double> _getScoreForDate(DateTime date) async {
    try {
      // Calculate completion for the specific date based on time scale
      switch (widget.timeScale) {
        case TimeScale.day:
          // For day scale, check if habit was completed on this specific date
          final isCompleted = widget.analyticsService.habit.isCompletedOnDate(date);
          return isCompleted ? 100.0 : 0.0;
          
        case TimeScale.week:
          // For week scale, calculate completion percentage for the week containing this date
          final weekBoundaries = await widget.analyticsService.getWeekBoundaries(date);
          final weekStart = weekBoundaries['startDate']!;
          final weekEnd = weekBoundaries['endDate']!;
          return _calculateCompletionPercentageForPeriod(weekStart, weekEnd);
          
        case TimeScale.month:
          // For month scale, calculate completion percentage for the month containing this date
          final monthStart = DateTime(date.year, date.month, 1);
          final monthEnd = DateTime(date.year, date.month + 1, 0);
          return _calculateCompletionPercentageForPeriod(monthStart, monthEnd);
          
        case TimeScale.quarter:
          // For quarter scale, calculate completion percentage for the quarter containing this date
          final quarterStartMonth = ((date.month - 1) ~/ 3) * 3 + 1;
          final quarterStart = DateTime(date.year, quarterStartMonth, 1);
          final quarterEnd = DateTime(date.year, quarterStartMonth + 3, 0);
          return _calculateCompletionPercentageForPeriod(quarterStart, quarterEnd);
          
        case TimeScale.year:
          // For year scale, calculate completion percentage for the year containing this date
          final yearStart = DateTime(date.year, 1, 1);
          final yearEnd = DateTime(date.year, 12, 31);
          return _calculateCompletionPercentageForPeriod(yearStart, yearEnd);
      }
    } catch (e) {
      developer.log('FoundationScoreChart: Error getting score for date $date: $e');
      return 0.0;
    }
  }

  /// Calculate completion percentage for a specific period
  double _calculateCompletionPercentageForPeriod(DateTime start, DateTime end) {
    final entries = widget.analyticsService.entries;
    final habit = widget.analyticsService.habit;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    int totalDays = 0;
    int completedDays = 0;
    
    DateTime current = start;
    while (current.isBefore(end.add(const Duration(days: 1)))) {
      final currentDate = DateTime(current.year, current.month, current.day);
      
      // Only count days up to today for current/future periods
      if (currentDate.isAfter(today)) {
        break;
      }
      
      totalDays++;
      
      // Check if habit was completed on this date
      if (habit.isCompletedOnDate(currentDate)) {
        completedDays++;
      }
      
      current = current.add(const Duration(days: 1));
    }
    
    return totalDays > 0 ? (completedDays / totalDays) * 100 : 0.0;
  }

  /// Handle scroll events - SIMPLIFIED
  void _onScroll() {
    // Scroll handling for current period positioning
    if (!_scrollController.hasClients) return;
    // Future: Add infinite scroll if needed
  }

  /// Get previous date based on time scale
  DateTime _getPreviousDate(DateTime date) {
    switch (widget.timeScale) {
      case TimeScale.day:
        return date.subtract(Duration(days: 1));
      case TimeScale.week:
        return date.subtract(Duration(days: 7));
      case TimeScale.month:
        int prevMonth = date.month - 1;
        int prevYear = date.year;
        if (prevMonth <= 0) {
          prevMonth = 12;
          prevYear -= 1;
        }
        return DateTime(prevYear, prevMonth, date.day.clamp(1, 28));
      case TimeScale.quarter:
        int prevMonth = date.month - 3;
        int prevYear = date.year;
        while (prevMonth <= 0) {
          prevMonth += 12;
          prevYear -= 1;
        }
        return DateTime(prevYear, prevMonth, date.day.clamp(1, 28));
      case TimeScale.year:
        return DateTime(date.year - 1, date.month, date.day.clamp(1, 28));
    }
  }

  /// Format date label based on time scale with correct formatting
  String _formatDateLabel(DateTime date) {
    switch (widget.timeScale) {
      case TimeScale.day:
        // Format: "15-Tu" (day-weekday abbreviation)
        return '${date.day}-${_getDayAbbreviation(date.weekday)}';
      case TimeScale.week:
        final weekNumber = _getWeekNumber(date);
        return 'W$weekNumber';
      case TimeScale.month:
        // Format: "Jul" or "Dec\n2025" for year transitions
        final monthAbbr = _getMonthAbbreviation(date.month);
        if (date.month == 1) {
          // Show year for January (new year)
          return '$monthAbbr\n${date.year}';
        }
        return monthAbbr;
      case TimeScale.quarter:
        // Format: "Q3" (quarter number)
        return 'Q${_getQuarter(date)}';
      case TimeScale.year:
        return '${date.year}';
    }
  }

  /// Get day abbreviation for weekday
  String _getDayAbbreviation(int weekday) {
    const days = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];
    return days[weekday - 1]; // weekday is 1-7, array is 0-6
  }

  String _getMonthAbbreviation(int month) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[month - 1];
  }

  int _getWeekNumber(DateTime date) {
    // ISO 8601 week numbering: week starts on Monday
    final startOfYear = DateTime(date.year, 1, 1);
    final firstMonday = startOfYear.subtract(Duration(days: (startOfYear.weekday - 1) % 7));
    final daysSinceFirstMonday = date.difference(firstMonday).inDays;
    final weekNumber = (daysSinceFirstMonday / 7).floor() + 1;
    
    // Handle edge cases for week 53/1
    if (weekNumber <= 0) {
      // This date belongs to the last week of the previous year
      return _getWeekNumber(DateTime(date.year - 1, 12, 31));
    } else if (weekNumber > 52) {
      // Check if this should be week 1 of next year
      final lastDayOfYear = DateTime(date.year, 12, 31);
      final lastWeekNumber = _getWeekNumber(lastDayOfYear);
      if (lastWeekNumber == 53) {
        return 53;
      } else {
        return 1; // This is week 1 of next year
      }
    }
    
    return weekNumber;
  }

  int _getQuarter(DateTime date) {
    return ((date.month - 1) / 3).floor() + 1;
  }

  /// Schedule scroll to recent data with multiple retry attempts
  void _scheduleScrollToRecentData() {
    developer.log('FoundationScoreChart: === SCHEDULING SCROLL TO RECENT DATA ===');
    
    // Try multiple times with increasing delays to ensure layout is complete
    Future.delayed(const Duration(milliseconds: 100), () => _scrollToRecentData());
    Future.delayed(const Duration(milliseconds: 300), () => _scrollToRecentData());
    Future.delayed(const Duration(milliseconds: 600), () => _scrollToRecentData());
    Future.delayed(const Duration(milliseconds: 1000), () => _scrollToRecentData());
  }

  /// Scroll to show recent data with current period visible
  void _scrollToRecentData() {
    developer.log('FoundationScoreChart: === _scrollToRecentData CALLED ===');
    developer.log('FoundationScoreChart: Has clients: ${_scrollController.hasClients}');
    developer.log('FoundationScoreChart: Data points: ${_allDataPoints.length}');
    developer.log('FoundationScoreChart: Time scale: ${widget.timeScale.name}');
    
    if (!_scrollController.hasClients || _allDataPoints.isEmpty) {
      developer.log('FoundationScoreChart: Early return - no clients or empty data');
      return;
    }
    
    try {
      final maxScrollExtent = _scrollController.position.maxScrollExtent;
      final viewportWidth = _scrollController.position.viewportDimension;
      final totalWidth = _allDataPoints.length * _columnWidth;
      
      developer.log('FoundationScoreChart: Viewport: $viewportWidth, Total: $totalWidth, Max scroll: $maxScrollExtent');
      
      // BUGFIX: Find the current period in the data
      final now = DateTime.now();
      int currentPeriodIndex = -1;
      
      developer.log('FoundationScoreChart: Looking for current period in ${_allDataPoints.length} data points');
      developer.log('FoundationScoreChart: Current time: $now');
      
      for (int i = 0; i < _allDataPoints.length; i++) {
        final point = _allDataPoints[i];
        final isCurrentPeriod = _isCurrentPeriod(point.date, now);
        developer.log('FoundationScoreChart: Point $i: ${point.date} -> isCurrent: $isCurrentPeriod');
        
        if (isCurrentPeriod) {
          currentPeriodIndex = i;
          developer.log('FoundationScoreChart: Found current period at index $i');
          break;
        }
      }
      
      if (currentPeriodIndex == -1) {
        // Fallback: scroll to show the rightmost data
        final targetOffset = totalWidth - viewportWidth;
        final finalOffset = targetOffset.clamp(0.0, maxScrollExtent);
        _scrollController.jumpTo(finalOffset);
        developer.log('FoundationScoreChart: No current period found, scrolled to end at offset: $finalOffset');
        return;
      }
      
      // BUGFIX: Calculate offset to center the current period in viewport
      final currentPeriodX = currentPeriodIndex * _columnWidth;
      final targetOffset = currentPeriodX - (viewportWidth / 2) + (_columnWidth / 2);
      final finalOffset = targetOffset.clamp(0.0, maxScrollExtent);
      
      developer.log('FoundationScoreChart: Current period X: $currentPeriodX, Target offset: $targetOffset, Final offset: $finalOffset');
      
      _scrollController.animateTo(
        finalOffset,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOutCubic,
      );
      
      developer.log('FoundationScoreChart: Scrolled to current period at index $currentPeriodIndex, offset: $finalOffset');
    } catch (e) {
      developer.log('FoundationScoreChart: Error scrolling to recent data: $e');
    }
  }
  
  /// Check if a date represents the current period for the given time scale
  bool _isCurrentPeriod(DateTime date, DateTime now) {
    developer.log('FoundationScoreChart: _isCurrentPeriod checking ${widget.timeScale.name}: $date vs $now');
    
    bool result = false;
    switch (widget.timeScale) {
      case TimeScale.day:
        result = date.year == now.year && date.month == now.month && date.day == now.day;
        developer.log('FoundationScoreChart: Day check: ${date.year}/${date.month}/${date.day} vs ${now.year}/${now.month}/${now.day} = $result');
        break;
      case TimeScale.week:
        // BUGFIX: Use proper week boundaries that match the analytics service
        final currentWeekStart = now.subtract(Duration(days: now.weekday - 1));
        final currentWeekEnd = currentWeekStart.add(Duration(days: 6));
        result = date.isAfter(currentWeekStart.subtract(Duration(days: 1))) && 
                 date.isBefore(currentWeekEnd.add(Duration(days: 1)));
        developer.log('FoundationScoreChart: Week check: $date in range ${currentWeekStart} to ${currentWeekEnd} = $result');
        break;
      case TimeScale.month:
        result = date.year == now.year && date.month == now.month;
        developer.log('FoundationScoreChart: Month check: ${date.year}/${date.month} vs ${now.year}/${now.month} = $result');
        break;
      case TimeScale.quarter:
        final currentQuarter = ((now.month - 1) ~/ 3) + 1;
        final dateQuarter = ((date.month - 1) ~/ 3) + 1;
        result = date.year == now.year && dateQuarter == currentQuarter;
        developer.log('FoundationScoreChart: Quarter check: ${date.year}/Q${dateQuarter} vs ${now.year}/Q${currentQuarter} = $result');
        break;
      case TimeScale.year:
        result = date.year == now.year;
        developer.log('FoundationScoreChart: Year check: ${date.year} vs ${now.year} = $result');
        break;
    }
    
    return result;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: widget.height,
      width: double.infinity,
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _allDataPoints.isEmpty
              ? _buildEmptyState(theme)
              : _buildChart(theme),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Text(
        'No data available',
        style: GoogleFonts.inter(
          color: theme.colorScheme.onSurfaceVariant,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildChart(ThemeData theme) {
    return LayoutBuilder(
      builder: (context, constraints) {
        _calculateLayout(constraints);
        
        return Row(
          children: [
            // Y-axis labels (fixed)
            _buildYAxisLabels(theme),
            
            // Chart area (scrollable)
            Expanded(
              child: _buildScrollableChart(theme),
            ),
          ],
        );
      },
    );
  }

  void _calculateLayout(BoxConstraints constraints) {
    final availableWidth = constraints.maxWidth - _yAxisWidth;
    _columnWidth = availableWidth / _visibleDataPoints;
    
    // Calculate chart area with proper padding
    _chartAreaHeight = widget.height - _topPadding - _bottomPadding;
    _baselineY = widget.height - _bottomPadding;
    
    developer.log('Chart Layout: width=${constraints.maxWidth}, height=${widget.height}, chartArea=$_chartAreaHeight, baseline=$_baselineY');
  }

  Widget _buildYAxisLabels(ThemeData theme) {
    return Container(
      width: _yAxisWidth,
      height: widget.height,
      child: CustomPaint(
        painter: _YAxisPainter(
          theme: theme,
          chartAreaHeight: _chartAreaHeight,
          baselineY: _baselineY,
          topPadding: _topPadding,
        ),
      ),
    );
  }

  Widget _buildScrollableChart(ThemeData theme) {
    return Container(
      height: widget.height,
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        child: Container(
          width: _allDataPoints.length * _columnWidth,
          child: CustomPaint(
            painter: _ChartPainter(
              dataPoints: _allDataPoints,
              theme: theme,
              columnWidth: _columnWidth,
              chartAreaHeight: _chartAreaHeight,
              baselineY: _baselineY,
              topPadding: _topPadding,
              bottomPadding: _bottomPadding,
            ),
          ),
        ),
      ),
    );
  }

  // REMOVED: Old methods replaced by new _buildChart implementation
}

/// Custom painter for the Y-axis labels with grid lines
class _YAxisLabelsPainter extends CustomPainter {
  final ThemeData theme;
  final double chartHeight;
  final double em;

  _YAxisLabelsPainter({
    required this.theme,
    required this.chartHeight,
    required this.em,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // BUGFIX: Calculate layout with proper spacing for labels
    final footerHeight = 4 * em; // More space for X-axis labels
    final labelSpaceAtTop = 4 * em; // BUGFIX: More space for percentage labels
    final internalPaddingTop = em;
    final baselineY = chartHeight - footerHeight;
    // BUGFIX: Chart area accounts for both top and bottom label space
    final chartAreaHeight = chartHeight - footerHeight - internalPaddingTop - labelSpaceAtTop;

    // ENHANCEMENT: Draw multiple Y-axis labels (0%, 25%, 50%, 75%, 100%)
    final percentageLabels = [0, 25, 50, 75, 100];
    
    for (final percentage in percentageLabels) {
      // BUGFIX: Y-axis labels must match exact chart coordinate calculation
      final normalizedValue = percentage / 100.0;
      final labelY = baselineY - (normalizedValue * chartAreaHeight);
      
      final textPainter = TextPainter(
        text: TextSpan(
          text: '$percentage%',
          style: GoogleFonts.inter(
            fontSize: em * 0.7, // Slightly smaller for multiple labels
            fontWeight: percentage == 0 ? FontWeight.w600 : FontWeight.w500,
            color: percentage == 0 
                ? theme.colorScheme.onSurface // Emphasize baseline
                : theme.colorScheme.onSurfaceVariant,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.right,
      );
      
      textPainter.layout();
      
      // Position label to the right of the sticky area
      final labelX = size.width - textPainter.width - (0.3 * em);
      final adjustedLabelY = labelY - textPainter.height / 2;
      
      textPainter.paint(canvas, Offset(labelX, adjustedLabelY));
      
      // ENHANCEMENT: Draw subtle grid lines for reference
      if (percentage > 0) { // Don't draw grid line for 0% (baseline already exists)
        final gridPaint = Paint()
          ..color = theme.colorScheme.outline.withOpacity(0.2)
          ..strokeWidth = 0.5
          ..style = PaintingStyle.stroke;
        
        canvas.drawLine(
          Offset(0, labelY),
          Offset(size.width, labelY),
          gridPaint,
        );
      } else {
        // BUGFIX: Ensure 0% grid line aligns perfectly with baseline
        final baselinePaint = Paint()
          ..color = theme.colorScheme.outline.withOpacity(0.4)
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke;
        
        canvas.drawLine(
          Offset(0, baselineY),
          Offset(size.width, baselineY),
          baselinePaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant _YAxisLabelsPainter oldDelegate) {
    return oldDelegate.chartHeight != chartHeight ||
           oldDelegate.em != em;
  }
}

/// Chart painter - REBUILT
class _ChartPainter extends CustomPainter {
  final List<ChartDataPoint> dataPoints;
  final ThemeData theme;
  final double columnWidth;
  final double chartAreaHeight;
  final double baselineY;
  final double topPadding;
  final double bottomPadding;

  _ChartPainter({
    required this.dataPoints,
    required this.theme,
    required this.columnWidth,
    required this.chartAreaHeight,
    required this.baselineY,
    required this.topPadding,
    required this.bottomPadding,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.isEmpty) return;
    
    // Draw grid lines
    _drawGridLines(canvas, size);
    
    // Draw chart line and points
    _drawChartLine(canvas, size);
    
    // Draw value labels
    _drawValueLabels(canvas, size);
    
    // Draw X-axis labels
    _drawXAxisLabels(canvas, size);
  }
  
  void _drawGridLines(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = theme.colorScheme.outline.withOpacity(0.3)
      ..strokeWidth = 1.0;
    
    // Horizontal grid lines
    for (int percentage = 0; percentage <= 100; percentage += 25) {
      final y = baselineY - (percentage / 100.0 * chartAreaHeight);
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }
  }
  
  void _drawChartLine(Canvas canvas, Size size) {
    final linePaint = Paint()
      ..color = theme.colorScheme.primary
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;
    
    final pointPaint = Paint()
      ..color = theme.colorScheme.primary
      ..style = PaintingStyle.fill;
    
    final path = Path();
    bool pathStarted = false;
    
    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      final x = i * columnWidth + columnWidth / 2;
      final y = baselineY - (point.value / 100.0 * chartAreaHeight);
      
      if (!pathStarted) {
        path.moveTo(x, y);
        pathStarted = true;
      } else {
        path.lineTo(x, y);
      }
      
      // Draw point
      canvas.drawCircle(Offset(x, y), point.isFuture ? 3.0 : 4.0, pointPaint);
    }
    
    canvas.drawPath(path, linePaint);
  }
  
  void _drawValueLabels(Canvas canvas, Size size) {
    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      if (point.value <= 0) continue;
      
      final x = i * columnWidth + columnWidth / 2;
      final y = baselineY - (point.value / 100.0 * chartAreaHeight);
      
      final textPainter = TextPainter(
        text: TextSpan(
          text: '${point.value.toInt()}%',
          style: GoogleFonts.inter(
            fontSize: 7, // REFINEMENT: Further reduced from 9 to 7 for more compact labels
            fontWeight: FontWeight.w600, // REFINEMENT: Increased weight for shine effect
            color: Colors.white, // REFINEMENT: Use white for better contrast and elegance
            shadows: [
              Shadow(
                offset: Offset(0.5, 0.5),
                blurRadius: 1.5,
                color: theme.colorScheme.primary.withOpacity(0.7), // REFINEMENT: Primary color shadow for complementary glow
              ),
            ],
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      
      textPainter.layout();
      
      // REFINEMENT: Remove background and borders, increase rotation, add shine
      canvas.save();
      canvas.translate(x, y - 18); // Slightly closer to data point
      canvas.rotate(-0.5); // REFINEMENT: Increased rotation from -0.3 to -0.5 (-28 degrees) for better spacing
      textPainter.paint(canvas, Offset(-textPainter.width / 2, -textPainter.height / 2));
      canvas.restore();
    }
  }
  
  void _drawXAxisLabels(Canvas canvas, Size size) {
    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      final x = i * columnWidth + columnWidth / 2;
      
      final textPainter = TextPainter(
        text: TextSpan(
          text: point.label,
          style: GoogleFonts.inter(
            fontSize: 10,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, baselineY + 10));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Y-axis painter - REBUILT
class _YAxisPainter extends CustomPainter {
  final ThemeData theme;
  final double chartAreaHeight;
  final double baselineY;
  final double topPadding;

  _YAxisPainter({
    required this.theme,
    required this.chartAreaHeight,
    required this.baselineY,
    required this.topPadding,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final percentages = [0, 25, 50, 75, 100];
    
    for (final percentage in percentages) {
      final y = baselineY - (percentage / 100.0 * chartAreaHeight);
      
      // Draw label
      final textPainter = TextPainter(
        text: TextSpan(
          text: '$percentage%',
          style: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: percentage == 0 ? FontWeight.w600 : FontWeight.w500,
            color: percentage == 0 
                ? theme.colorScheme.onSurface
                : theme.colorScheme.onSurfaceVariant,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.right,
      );
      
      textPainter.layout();
      textPainter.paint(canvas, Offset(size.width - textPainter.width - 5, y - textPainter.height / 2));
      
      // Draw grid line
      final gridPaint = Paint()
        ..color = theme.colorScheme.outline.withOpacity(percentage == 0 ? 0.6 : 0.3)
        ..strokeWidth = percentage == 0 ? 2.0 : 1.0;
      
      canvas.drawLine(Offset(size.width - 5, y), Offset(size.width, y), gridPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}