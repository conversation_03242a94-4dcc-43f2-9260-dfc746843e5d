<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phase 1 Analytics - Dark Theme</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .material-icons { vertical-align: middle; }
    </style>
</head>
<body class="bg-gray-900">
    <div class="max-w-6xl mx-auto p-8">
        <h1 class="text-3xl font-bold text-gray-100 mb-2">Phase 1: Dark Theme Preview</h1>
        <p class="text-gray-400 mb-8">How analytics features will look in dark mode</p>

        <!-- Section 1: Enhanced Habit List with Scores -->
        <section class="mb-12">
            <h2 class="text-xl font-semibold text-gray-100 mb-4">Enhanced Habit List - Dark Theme</h2>
            <div class="bg-gray-800 rounded-lg shadow-sm border border-gray-700">
                <!-- Header -->
                <div class="px-6 py-4 border-b border-gray-700">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-100">All Habits</h3>
                        <button class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 flex items-center gap-2">
                            <span class="material-icons text-sm">add</span>
                            Add Habit
                        </button>
                    </div>
                </div>
                
                <!-- Habit Items -->
                <div class="divide-y divide-gray-700">
                    <!-- Boolean Habit with Score -->
                    <div class="px-6 py-4 hover:bg-gray-750">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-1 h-12 bg-purple-500 rounded-full"></div>
                                <div>
                                    <h4 class="font-medium text-gray-100">Morning Exercise</h4>
                                    <div class="flex items-center gap-4 mt-1">
                                        <span class="text-sm text-gray-400">Daily</span>
                                        <span class="text-sm text-purple-400 font-medium">Score: 85%</span>
                                        <span class="text-sm text-green-400 font-medium">🔥 15 day streak</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <button class="w-10 h-10 rounded-md border-2 border-green-500 bg-green-500 flex items-center justify-center">
                                    <span class="material-icons text-white text-lg">check</span>
                                </button>
                                <button class="text-gray-400 hover:text-gray-200">
                                    <span class="material-icons">more_vert</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Numerical Habit with Score -->
                    <div class="px-6 py-4 hover:bg-gray-750">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-1 h-12 bg-blue-500 rounded-full"></div>
                                <div>
                                    <h4 class="font-medium text-gray-100">Water Intake</h4>
                                    <div class="flex items-center gap-4 mt-1">
                                        <span class="text-sm text-gray-400">Target: 8 glasses</span>
                                        <span class="text-sm text-purple-400 font-medium">Score: 72%</span>
                                        <span class="text-sm text-orange-400 font-medium">🔥 3 day streak</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="flex items-center gap-1 bg-gray-700 rounded-md px-2 py-1">
                                    <input type="number" value="6" min="0" max="20" class="w-12 text-center bg-transparent outline-none font-medium text-gray-100">
                                    <span class="text-sm text-gray-400">/ 8</span>
                                </div>
                                <button class="text-gray-400 hover:text-gray-200">
                                    <span class="material-icons">more_vert</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Habit with Note -->
                    <div class="px-6 py-4 hover:bg-gray-750">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-1 h-12 bg-green-500 rounded-full"></div>
                                <div>
                                    <h4 class="font-medium text-gray-100">Reading</h4>
                                    <div class="flex items-center gap-4 mt-1">
                                        <span class="text-sm text-gray-400">Daily</span>
                                        <span class="text-sm text-purple-400 font-medium">Score: 92%</span>
                                        <span class="text-sm text-gray-500 italic">📝 "Finished chapter 3"</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <button class="w-10 h-10 rounded-md border-2 border-green-500 bg-green-500 flex items-center justify-center">
                                    <span class="material-icons text-white text-lg">check</span>
                                </button>
                                <button class="text-gray-400 hover:text-gray-200">
                                    <span class="material-icons">more_vert</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2: Score Progress Bars -->
        <section class="mb-12">
            <h2 class="text-xl font-semibold text-gray-100 mb-4">Score Visualization Components</h2>
            <div class="grid md:grid-cols-3 gap-4">
                <!-- Circular Progress -->
                <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
                    <h3 class="font-medium text-gray-200 mb-4">Circular Score</h3>
                    <div class="relative w-32 h-32 mx-auto">
                        <svg class="w-32 h-32 transform -rotate-90">
                            <circle cx="64" cy="64" r="56" stroke="#374151" stroke-width="12" fill="none"/>
                            <circle cx="64" cy="64" r="56" stroke="#9333EA" stroke-width="12" fill="none"
                                stroke-dasharray="351.86" stroke-dashoffset="70.37" stroke-linecap="round"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-3xl font-bold text-gray-100">85%</span>
                        </div>
                    </div>
                </div>

                <!-- Linear Progress -->
                <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
                    <h3 class="font-medium text-gray-200 mb-4">Linear Score</h3>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm text-gray-400 mb-1">
                                <span>Exercise</span>
                                <span>85%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm text-gray-400 mb-1">
                                <span>Water</span>
                                <span>72%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 72%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm text-gray-400 mb-1">
                                <span>Reading</span>
                                <span>92%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 92%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Streak Display -->
                <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
                    <h3 class="font-medium text-gray-200 mb-4">Streak Display</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Current</span>
                            <div class="flex items-center gap-2">
                                <span class="text-2xl">🔥</span>
                                <span class="text-xl font-bold text-orange-400">15 days</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Best</span>
                            <div class="flex items-center gap-2">
                                <span class="text-2xl">⭐</span>
                                <span class="text-xl font-bold text-yellow-400">42 days</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Total</span>
                            <span class="text-lg font-medium text-gray-300">128 days</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
