import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ModernTheme {
  // Spacing System - Compact Design
  static const double spaceXXS = 2.0;   // Fine details, borders
  static const double spaceXS = 4.0;    // Cell spacing, tight layouts
  static const double spaceSM = 8.0;    // Compact padding, button internals (25% reduction from 12px)
  static const double spaceMD = 12.0;   // Standard component padding (25% reduction from 16px)
  static const double spaceLG = 16.0;   // Section spacing, comfortable layouts
  static const double spaceXL = 20.0;   // Large component separation
  static const double spaceXXL = 24.0;  // Major layout divisions
  static const double spaceXXXL = 32.0; // Page-level spacing
  
  // Component-specific sizes
  static const double completionIndicatorSize = 18.0;
  static const double completionIndicatorSizeDark = 21.6; // 20% larger in dark mode
  static const double minTouchTarget = 44.0; // Accessibility requirement

  // COMPREHENSIVE DEBUGGING: Add missing static theme properties for analytics screen
  static const Color backgroundColor = lightBackground;
  static const Color textColor = lightTextPrimary;
  static const Color cardColor = lightSurfaceVariant;
  static const Color primaryColor = lightAccent;
  // Light Theme Colors - Refined Minimalist Design
  static const Color lightBackground = Color(0xFFFFFFFF);        // Snow White
  static const Color lightTextPrimary = Color(0xFF2D3748);       // Graphite
  static const Color lightTextSecondary = Color(0xFF718096);     // Silver
  static const Color lightAccent = Color(0xFF38B2AC);            // Seafoam
  static const Color lightDivider = Color(0xFFE2E8F0);           // Cloud
  static const Color lightSurfaceVariant = Color(0xFFF7FAFC);    // Whisper
  
  // Legacy colors for compatibility
  static const Color lightPrimary = Color(0xFF38B2AC);           // Using accent as primary
  static const Color lightPrimaryVariant = Color(0xFF319795);    // Darker seafoam
  static const Color lightSecondary = Color(0xFF38B2AC);         // Same as accent
  static const Color lightSurface = Color(0xFFF7FAFC);           // Whisper for cards
  static const Color lightOnPrimary = Color(0xFFFFFFFF);         // White text on primary
  static const Color lightOnSurface = Color(0xFF2D3748);         // Graphite
  static const Color lightOnSurfaceVariant = Color(0xFF718096);  // Silver
  static const Color lightOnSurfaceSecondary = Color(0xFF718096); // Silver
  static const Color lightSuccess = Color(0xFF38B2AC);           // Using accent
  static const Color lightSuccessVariant = Color(0xFF319795);    // Darker seafoam
  static const Color lightCardShadow = Color(0x08000000);        // Softer shadow
  static const Color lightBorder = Color(0xFFE2E8F0);            // Cloud
  static const Color lightGridLines = Color(0xFFF7FAFC);         // Whisper

  // Dark Theme Colors - True Dark Night Theme
  static const Color darkBackground = Color(0xFF121826);         // Night
  static const Color darkTextPrimary = Color(0xFFE2E8F0);        // Moonstone
  static const Color darkTextSecondary = Color(0xFFA0AEC0);      // Frost
  static const Color darkAccent = Color(0xFF81E6D9);             // Aquamarine
  static const Color darkDivider = Color(0xFF2D3748);            // Deep Space
  static const Color darkSurfaceVariant = Color(0xFF1A202C);     // Twilight
  
  // Legacy colors for compatibility
  static const Color darkPrimary = Color(0xFF81E6D9);
  static const Color darkPrimaryVariant = Color(0xFF4FD1C7);     // Brighter aquamarine
  static const Color darkSecondary = Color(0xFF81E6D9);          // Same as accent
  static const Color darkSurface = Color(0xFF1A202C);            // Twilight for cards
  static const Color darkOnPrimary = Color(0xFF121826);          // Night text on bright primary
  static const Color darkOnSurface = Color(0xFFE2E8F0);          // Moonstone
  static const Color darkOnSurfaceVariant = Color(0xFFA0AEC0);   // Frost
  static const Color darkOnSurfaceSecondary = Color(0xFFA0AEC0); // Frost
  static const Color darkSuccess = Color(0xFF81E6D9);            // Using accent
  static const Color darkSuccessVariant = Color(0xFF4FD1C7);     // Brighter aquamarine
  static const Color darkCardShadow = Color(0x20000000);         // Subtle shadow for depth
  static const Color darkBorder = Color(0xFF2D3748);             // Deep Space
  static const Color darkGridLines = Color(0xFF1A202C);          // Twilight

  static ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    useMaterial3: true,
    colorScheme: const ColorScheme.light(
      primary: lightPrimary,
      primaryContainer: lightPrimaryVariant,
      secondary: lightSecondary,
      surface: lightSurface,
      surfaceContainerHighest: lightSurfaceVariant,
      background: lightBackground,
      onPrimary: lightOnPrimary,
      onSurface: lightOnSurface,
      onSurfaceVariant: lightOnSurfaceVariant,
    ),
    scaffoldBackgroundColor: lightBackground,
    cardColor: lightSurface,
    dividerColor: lightBorder,
    textTheme: GoogleFonts.interTextTheme().copyWith(
      // Refined typography system - 10% size reduction with design system specs
      displaySmall: GoogleFonts.inter(
        fontSize: 14, // Section headers - design system spec
        fontWeight: FontWeight.w600, // Medium weight
        color: lightTextPrimary,
        height: 1.2,
        letterSpacing: -0.01,
      ),
      headlineLarge: GoogleFonts.inter(
        fontSize: 18, // 20 * 0.9
        fontWeight: FontWeight.bold,
        color: lightTextPrimary,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: 16.2, // 18 * 0.9
        fontWeight: FontWeight.w600,
        color: lightTextPrimary,
      ),
      titleMedium: GoogleFonts.inter(
        fontSize: 14.4, // 16 * 0.9
        fontWeight: FontWeight.w500,
        color: lightTextPrimary,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: 12, // Body text - design system spec
        fontWeight: FontWeight.w400, // Regular weight
        color: lightTextPrimary,
        height: 1.25,
        letterSpacing: 0,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: 10.8, // 12 * 0.9
        fontWeight: FontWeight.normal,
        color: lightTextSecondary,
      ),
      labelSmall: GoogleFonts.robotoMono(
        fontSize: 10, // Dates and percentages - design system spec
        fontWeight: FontWeight.w400, // Regular weight
        color: lightTextSecondary,
        height: 1.3,
        letterSpacing: 0.05,
      ),
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: lightSurfaceVariant,
      foregroundColor: lightOnSurface,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: lightOnSurface,
      ),
      iconTheme: const IconThemeData(color: lightOnSurface),
    ),
    cardTheme: CardThemeData(
      color: lightSurface,
      elevation: 1.5,
      shadowColor: lightCardShadow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: lightPrimary,
        foregroundColor: lightOnPrimary,
        elevation: 1.5,
        shadowColor: lightCardShadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    ),
    chipTheme: ChipThemeData(
      backgroundColor: lightSurfaceVariant,
      selectedColor: lightPrimary,
      labelStyle: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: lightOnSurfaceVariant,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
    ),
  );

  static ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    useMaterial3: true,
    colorScheme: const ColorScheme.dark(
      primary: darkPrimary,
      primaryContainer: darkPrimaryVariant,
      secondary: darkSecondary,
      surface: darkSurface,
      surfaceContainerHighest: darkSurfaceVariant,
      background: darkBackground,
      onPrimary: darkOnPrimary,
      onSurface: darkOnSurface,
      onSurfaceVariant: darkOnSurfaceVariant,
    ),
    scaffoldBackgroundColor: darkBackground,
    cardColor: darkSurface,
    dividerColor: darkSurfaceVariant,
    textTheme: GoogleFonts.interTextTheme(ThemeData.dark().textTheme).copyWith(
      // Dark theme typography with 20% larger completion indicators
      displaySmall: GoogleFonts.inter(
        fontSize: 14, // Section headers
        fontWeight: FontWeight.w600,
        color: darkTextPrimary,
        height: 1.2,
        letterSpacing: -0.01,
      ),
      headlineLarge: GoogleFonts.inter(
        fontSize: 18, // 20 * 0.9
        fontWeight: FontWeight.bold,
        color: darkTextPrimary,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: 16.2, // 18 * 0.9
        fontWeight: FontWeight.w600,
        color: darkTextPrimary,
      ),
      titleMedium: GoogleFonts.inter(
        fontSize: 14.4, // 16 * 0.9
        fontWeight: FontWeight.w500,
        color: darkTextPrimary,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: 12, // Body text
        fontWeight: FontWeight.w400,
        color: darkTextPrimary,
        height: 1.25,
        letterSpacing: 0,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: 10.8, // 12 * 0.9
        fontWeight: FontWeight.normal,
        color: darkTextSecondary,
      ),
      labelSmall: GoogleFonts.robotoMono(
        fontSize: 10, // Dates and percentages
        fontWeight: FontWeight.w400,
        color: darkTextSecondary,
        height: 1.3,
        letterSpacing: 0.05,
      ),
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: darkBackground,
      foregroundColor: darkOnSurface,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: darkOnSurface,
      ),
      iconTheme: const IconThemeData(color: darkOnSurface),
    ),
    cardTheme: CardThemeData(
      color: darkSurface,
      elevation: 4,
      shadowColor: darkCardShadow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: darkPrimary,
        foregroundColor: darkOnPrimary,
        elevation: 2,
        shadowColor: darkCardShadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    ),
    chipTheme: ChipThemeData(
      backgroundColor: darkSurfaceVariant,
      selectedColor: darkPrimary,
      labelStyle: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: darkOnSurface,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
    ),
  );
}