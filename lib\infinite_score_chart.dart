import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:developer' as developer;
import 'habit_analytics_service.dart';

/// Infinite Scrolling Score Chart with Correct Viewport
/// 
/// This implementation addresses the two key issues:
/// 1. Implements infinite scrolling to view entire habit history
/// 2. Ensures exactly 14 data points are visible in the viewport at all times
/// 3. Maintains perfect baseline alignment for the "0%" label
class InfiniteScoreChart extends StatefulWidget {
  final HabitAnalyticsService analyticsService;
  final TimeScale timeScale;
  final ScrollController? controller;
  final double height;

  const InfiniteScoreChart({
    super.key,
    required this.analyticsService,
    required this.timeScale,
    this.controller,
    this.height = 200,
  });

  @override
  State<InfiniteScoreChart> createState() => _InfiniteScoreChartState();
}

class _InfiniteScoreChartState extends State<InfiniteScoreChart> {
  late ScrollController _scrollController;
  List<ChartDataPoint> _allDataPoints = [];
  bool _isLoading = true;
  bool _isLoadingOlder = false;
  
  // Layout constants
  static const double _stickyLabelWidth = 40.0;
  static const double _baselineStrokeWidth = 2.0;
  static const int _visibleDataPoints = 10; // REFINEMENT: Further reduced from 12 to 10 for optimal spacing
  
  // Dynamic layout variables
  double _columnWidth = 80.0; // SPACING FIX: Increased from 60.0 to 80.0 for better readability
  double _em = 0.0;
  
  // Infinite scroll management
  DateTime? _oldestLoadedDate;
  DateTime? _newestLoadedDate;
  static const int _loadBatchSize = 30; // Load 30 points at a time for smooth scrolling

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void didUpdateWidget(InfiniteScoreChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.timeScale != widget.timeScale) {
      _loadInitialData();
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  /// Load initial data - enough to fill viewport plus buffer for scrolling
  Future<void> _loadInitialData() async {
    setState(() => _isLoading = true);
    
    try {
      developer.log('InfiniteScoreChart: Loading initial data for ${widget.timeScale.name}');
      
      // Load initial batch: 14 visible + 30 buffer = 44 points total
      final points = await _generateDataPoints(44);
      
      setState(() {
        _allDataPoints = points;
        _isLoading = false;
        if (points.isNotEmpty) {
          _oldestLoadedDate = points.first.date;
          _newestLoadedDate = points.last.date;
        }
      });
      
      // Scroll to show recent data with proper positioning
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToRecentData();
      });
      
      developer.log('InfiniteScoreChart: Loaded ${points.length} initial data points');
    } catch (e) {
      developer.log('InfiniteScoreChart: Error loading initial data: $e');
      setState(() => _isLoading = false);
    }
  }

  /// Generate data points with correct +2 period offset
  Future<List<ChartDataPoint>> _generateDataPoints(int count) async {
    final now = DateTime.now();
    final points = <ChartDataPoint>[];
    
    // Calculate the end date: current period + 2 periods
    final endDate = _getNextDate(_getNextDate(_getCurrentPeriodStart(now)));
    
    // Start from end date and go backwards to generate the timeline
    DateTime current = endDate;
    for (int i = 0; i < count; i++) {
      // Get actual score data for this date
      final score = await _getScoreForDate(current);
      
      points.insert(0, ChartDataPoint(
        date: current,
        value: score,
        label: _formatDateLabel(current),
        isFuture: current.isAfter(now),
      ));
      
      current = _getPreviousDate(current);
    }
    
    developer.log('InfiniteScoreChart: Generated timeline from ${points.first.date} to ${points.last.date} (end: $endDate)');
    return points;
  }

  /// Get the start of the current period based on time scale
  DateTime _getCurrentPeriodStart(DateTime date) {
    switch (widget.timeScale) {
      case TimeScale.day:
        return DateTime(date.year, date.month, date.day);
      case TimeScale.week:
        // Get start of current week (Monday)
        final daysFromMonday = date.weekday - 1;
        return DateTime(date.year, date.month, date.day - daysFromMonday);
      case TimeScale.month:
        return DateTime(date.year, date.month, 1);
      case TimeScale.quarter:
        // Get start of current quarter
        final quarterStartMonth = ((date.month - 1) ~/ 3) * 3 + 1;
        return DateTime(date.year, quarterStartMonth, 1);
      case TimeScale.year:
        return DateTime(date.year, 1, 1);
    }
  }

  /// Get next date based on time scale
  DateTime _getNextDate(DateTime date) {
    switch (widget.timeScale) {
      case TimeScale.day:
        return date.add(Duration(days: 1));
      case TimeScale.week:
        return date.add(Duration(days: 7));
      case TimeScale.month:
        int nextMonth = date.month + 1;
        int nextYear = date.year;
        if (nextMonth > 12) {
          nextMonth = 1;
          nextYear += 1;
        }
        return DateTime(nextYear, nextMonth, date.day.clamp(1, 28));
      case TimeScale.quarter:
        int nextMonth = date.month + 3;
        int nextYear = date.year;
        while (nextMonth > 12) {
          nextMonth -= 12;
          nextYear += 1;
        }
        return DateTime(nextYear, nextMonth, date.day.clamp(1, 28));
      case TimeScale.year:
        return DateTime(date.year + 1, date.month, date.day.clamp(1, 28));
    }
  }

  /// Get score data for a specific date
  Future<double> _getScoreForDate(DateTime date) async {
    try {
      // Calculate completion for the specific date based on time scale
      switch (widget.timeScale) {
        case TimeScale.day:
          // For day scale, check if habit was completed on this specific date
          final isCompleted = widget.analyticsService.habit.isCompletedOnDate(date);
          return isCompleted ? 100.0 : 0.0;
          
        case TimeScale.week:
          // For week scale, calculate completion percentage for the week containing this date
          final weekBoundaries = await widget.analyticsService.getWeekBoundaries(date);
          final weekStart = weekBoundaries['startDate']!;
          final weekEnd = weekBoundaries['endDate']!;
          return _calculateCompletionPercentageForPeriod(weekStart, weekEnd);
          
        case TimeScale.month:
          // For month scale, calculate completion percentage for the month containing this date
          final monthStart = DateTime(date.year, date.month, 1);
          final monthEnd = DateTime(date.year, date.month + 1, 0);
          return _calculateCompletionPercentageForPeriod(monthStart, monthEnd);
          
        case TimeScale.quarter:
          // For quarter scale, calculate completion percentage for the quarter containing this date
          final quarterStartMonth = ((date.month - 1) ~/ 3) * 3 + 1;
          final quarterStart = DateTime(date.year, quarterStartMonth, 1);
          final quarterEnd = DateTime(date.year, quarterStartMonth + 3, 0);
          return _calculateCompletionPercentageForPeriod(quarterStart, quarterEnd);
          
        case TimeScale.year:
          // For year scale, calculate completion percentage for the year containing this date
          final yearStart = DateTime(date.year, 1, 1);
          final yearEnd = DateTime(date.year, 12, 31);
          return _calculateCompletionPercentageForPeriod(yearStart, yearEnd);
      }
    } catch (e) {
      developer.log('InfiniteScoreChart: Error getting score for date $date: $e');
      return 0.0;
    }
  }

  /// Calculate completion percentage for a specific period
  double _calculateCompletionPercentageForPeriod(DateTime start, DateTime end) {
    final entries = widget.analyticsService.entries;
    final habit = widget.analyticsService.habit;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    int totalDays = 0;
    int completedDays = 0;
    
    DateTime current = start;
    while (current.isBefore(end.add(const Duration(days: 1)))) {
      final currentDate = DateTime(current.year, current.month, current.day);
      
      // Only count days up to today for current/future periods
      if (currentDate.isAfter(today)) {
        break;
      }
      
      totalDays++;
      
      // Check if habit was completed on this date
      if (habit.isCompletedOnDate(currentDate)) {
        completedDays++;
      }
      
      current = current.add(const Duration(days: 1));
    }
    
    return totalDays > 0 ? (completedDays / totalDays) * 100 : 0.0;
  }

  /// Load older data when user scrolls to the left
  Future<void> _loadOlderData() async {
    if (_isLoadingOlder || _oldestLoadedDate == null) return;
    
    setState(() => _isLoadingOlder = true);
    
    try {
      developer.log('InfiniteScoreChart: Loading older data from ${_oldestLoadedDate}');
      
      // Generate older points starting from the oldest loaded date
      final olderPoints = <ChartDataPoint>[];
      DateTime current = _getPreviousDate(_oldestLoadedDate!);
      
      for (int i = 0; i < _loadBatchSize; i++) {
        final score = await _getScoreForDate(current);
        
        olderPoints.insert(0, ChartDataPoint(
          date: current,
          value: score,
          label: _formatDateLabel(current),
          isFuture: false, // Older dates are never future
        ));
        
        current = _getPreviousDate(current);
      }
      
      setState(() {
        _allDataPoints.insertAll(0, olderPoints);
        _oldestLoadedDate = olderPoints.first.date;
        _isLoadingOlder = false;
      });
      
      // Adjust scroll position to maintain user's view
      final addedWidth = olderPoints.length * _columnWidth;
      _scrollController.jumpTo(_scrollController.offset + addedWidth);
      
      developer.log('InfiniteScoreChart: Loaded ${olderPoints.length} older data points');
    } catch (e) {
      developer.log('InfiniteScoreChart: Error loading older data: $e');
      setState(() => _isLoadingOlder = false);
    }
  }

  /// Handle scroll events for infinite loading
  void _onScroll() {
    if (!_scrollController.hasClients) return;
    
    // Load older data when user scrolls near the beginning
    const loadThreshold = 200.0; // Load when within 200px of start
    if (_scrollController.offset <= loadThreshold && !_isLoadingOlder) {
      _loadOlderData();
    }
  }

  /// Scroll to show recent data with exactly 14 points visible
  void _scrollToRecentData() {
    if (!_scrollController.hasClients || _allDataPoints.isEmpty) return;
    
    try {
      final maxScrollExtent = _scrollController.position.maxScrollExtent;
      final viewportWidth = _scrollController.position.viewportDimension;
      
      // Calculate position to show exactly the last 14 points
      final targetWidth = _visibleDataPoints * _columnWidth;
      final totalWidth = _allDataPoints.length * _columnWidth;
      
      if (totalWidth <= viewportWidth) {
        // All data fits in viewport
        _scrollController.jumpTo(0);
      } else {
        // Scroll to show the rightmost 14 points
        final targetOffset = totalWidth - targetWidth;
        final finalOffset = targetOffset.clamp(0.0, maxScrollExtent);
        _scrollController.jumpTo(finalOffset);
      }
      
      developer.log('InfiniteScoreChart: Scrolled to show recent data - showing last $_visibleDataPoints points');
    } catch (e) {
      developer.log('InfiniteScoreChart: Error scrolling to recent data: $e');
    }
  }

  /// Get previous date based on time scale
  DateTime _getPreviousDate(DateTime date) {
    switch (widget.timeScale) {
      case TimeScale.day:
        return date.subtract(Duration(days: 1));
      case TimeScale.week:
        return date.subtract(Duration(days: 7));
      case TimeScale.month:
        int prevMonth = date.month - 1;
        int prevYear = date.year;
        if (prevMonth <= 0) {
          prevMonth = 12;
          prevYear -= 1;
        }
        return DateTime(prevYear, prevMonth, date.day.clamp(1, 28));
      case TimeScale.quarter:
        int prevMonth = date.month - 3;
        int prevYear = date.year;
        while (prevMonth <= 0) {
          prevMonth += 12;
          prevYear -= 1;
        }
        return DateTime(prevYear, prevMonth, date.day.clamp(1, 28));
      case TimeScale.year:
        return DateTime(date.year - 1, date.month, date.day.clamp(1, 28));
    }
  }

  /// Format date label based on time scale with correct formatting
  String _formatDateLabel(DateTime date) {
    switch (widget.timeScale) {
      case TimeScale.day:
        // Format: "15-Tu" (day-weekday abbreviation)
        return '${date.day}-${_getDayAbbreviation(date.weekday)}';
      case TimeScale.week:
        final weekNumber = _getWeekNumber(date);
        return 'W$weekNumber';
      case TimeScale.month:
        // Format: "Jul" or "Dec\n2025" for year transitions
        final monthAbbr = _getMonthAbbreviation(date.month);
        if (date.month == 1) {
          // Show year for January (new year)
          return '$monthAbbr\n${date.year}';
        }
        return monthAbbr;
      case TimeScale.quarter:
        // Format: "Q3" (quarter number)
        return 'Q${_getQuarter(date)}';
      case TimeScale.year:
        return '${date.year}';
    }
  }

  /// Get day abbreviation for weekday
  String _getDayAbbreviation(int weekday) {
    const days = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];
    return days[weekday - 1]; // weekday is 1-7, array is 0-6
  }

  String _getMonthAbbreviation(int month) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[month - 1];
  }

  int _getWeekNumber(DateTime date) {
    // ISO 8601 week numbering: week starts on Monday
    final startOfYear = DateTime(date.year, 1, 1);
    final firstMonday = startOfYear.subtract(Duration(days: (startOfYear.weekday - 1) % 7));
    final daysSinceFirstMonday = date.difference(firstMonday).inDays;
    final weekNumber = (daysSinceFirstMonday / 7).floor() + 1;
    
    // Handle edge cases for week 53/1
    if (weekNumber <= 0) {
      // This date belongs to the last week of the previous year
      return _getWeekNumber(DateTime(date.year - 1, 12, 31));
    } else if (weekNumber > 52) {
      // Check if this should be week 1 of next year
      final lastDayOfYear = DateTime(date.year, 12, 31);
      final lastWeekNumber = _getWeekNumber(lastDayOfYear);
      if (lastWeekNumber == 53) {
        return 53;
      } else {
        return 1; // This is week 1 of next year
      }
    }
    
    return weekNumber;
  }

  int _getQuarter(DateTime date) {
    return ((date.month - 1) / 3).floor() + 1;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: widget.height,
      width: double.infinity,
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _allDataPoints.isEmpty
              ? _buildEmptyState(theme)
              : _buildInfiniteChart(theme),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Text(
        'No data available',
        style: GoogleFonts.inter(
          color: theme.colorScheme.onSurfaceVariant,
          fontSize: 14,
        ),
      ),
    );
  }

  /// Build the infinite chart with correct viewport scaling
  Widget _buildInfiniteChart(ThemeData theme) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate layout to show exactly 14 data points
        _calculateViewportLayout(constraints.maxWidth, constraints.maxHeight);
        
        final totalChartWidth = _allDataPoints.length * _columnWidth;
        
        return Stack(
          children: [
            Row(
              children: [
                // Sticky "0%" label (doesn't scroll)
                Container(
                  width: _stickyLabelWidth,
                  height: widget.height,
                  child: CustomPaint(
                    painter: _StickyLabelPainter(
                      theme: theme,
                      chartHeight: widget.height,
                      em: _em,
                    ),
                  ),
                ),
                
                // Scrollable chart area
                Expanded(
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    scrollDirection: Axis.horizontal,
                    child: Container(
                      width: totalChartWidth,
                      height: widget.height,
                      child: CustomPaint(
                        painter: _InfiniteChartPainter(
                          dataPoints: _allDataPoints,
                          theme: theme,
                          columnWidth: _columnWidth,
                          chartHeight: widget.height,
                          em: _em,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            // Loading indicator for older data
            if (_isLoadingOlder)
              Positioned(
                left: 0,
                top: 0,
                child: Container(
                  width: 60,
                  height: 30,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Center(
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// Calculate layout to ensure exactly 14 data points are visible
  void _calculateViewportLayout(double width, double height) {
    // Calculate em unit
    _em = height * 0.06;
    
    // Calculate column width to show exactly 14 points in the available space
    final availableWidth = width - _stickyLabelWidth;
    _columnWidth = availableWidth / _visibleDataPoints;
    
    // SPACING FIX: Ensure minimum readable width with better spacing
    final minColumnWidth = _em * 4; // Increased from 3 to 4 for better spacing
    _columnWidth = _columnWidth.clamp(minColumnWidth, double.infinity);
    
    developer.log('InfiniteScoreChart: Viewport layout - width: $width, height: $height, columnWidth: $_columnWidth, visiblePoints: $_visibleDataPoints, em: $_em');
  }
}

/// Custom painter for the sticky "0%" label
class _StickyLabelPainter extends CustomPainter {
  final ThemeData theme;
  final double chartHeight;
  final double em;

  _StickyLabelPainter({
    required this.theme,
    required this.chartHeight,
    required this.em,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate baseline position
    final footerHeight = 3 * em;
    final baselineY = chartHeight - footerHeight;

    // Draw "0%" label aligned with baseline
    final textPainter = TextPainter(
      text: TextSpan(
        text: '0%',
        style: GoogleFonts.inter(
          fontSize: em * 0.8,
          fontWeight: FontWeight.w500,
          color: theme.colorScheme.onSurfaceVariant,
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.left,
    );
    
    textPainter.layout();
    
    // Position label perfectly aligned with baseline
    final labelY = baselineY - textPainter.height / 2;
    final labelX = 0.5 * em;
    
    textPainter.paint(canvas, Offset(labelX, labelY));
  }

  @override
  bool shouldRepaint(covariant _StickyLabelPainter oldDelegate) {
    return oldDelegate.chartHeight != chartHeight ||
           oldDelegate.em != em;
  }
}

/// Custom painter for the infinite chart
class _InfiniteChartPainter extends CustomPainter {
  final List<ChartDataPoint> dataPoints;
  final ThemeData theme;
  final double columnWidth;
  final double chartHeight;
  final double em;

  _InfiniteChartPainter({
    required this.dataPoints,
    required this.theme,
    required this.columnWidth,
    required this.chartHeight,
    required this.em,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate layout dimensions
    final footerHeight = 3 * em;
    final baselineY = chartHeight - footerHeight;
    final chartAreaHeight = chartHeight - footerHeight - em;

    // Draw the horizontal baseline (0% line)
    final baselinePaint = Paint()
      ..color = theme.colorScheme.outline
      ..strokeWidth = _InfiniteScoreChartState._baselineStrokeWidth
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(0, baselineY),
      Offset(size.width, baselineY),
      baselinePaint,
    );

    // Draw score line and data points
    _drawScoreLine(canvas, size, baselineY, chartAreaHeight);
    
    // Draw timeline labels
    _drawTimelineLabels(canvas, size, baselineY);
  }

  void _drawScoreLine(Canvas canvas, Size size, double baselineY, double chartAreaHeight) {
    if (dataPoints.length < 2) return;

    final linePaint = Paint()
      ..color = theme.colorScheme.primary
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final markerPaint = Paint()
      ..color = theme.colorScheme.primary
      ..style = PaintingStyle.fill;

    final path = Path();
    bool pathStarted = false;

    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      final x = i * columnWidth + columnWidth / 2;
      final y = baselineY - (point.value / 100.0 * chartAreaHeight);

      // Draw line
      if (!pathStarted) {
        path.moveTo(x, y);
        pathStarted = true;
      } else {
        path.lineTo(x, y);
      }

      // Draw marker
      canvas.drawCircle(Offset(x, y), 3.0, markerPaint);
    }

    canvas.drawPath(path, linePaint);
  }

  void _drawTimelineLabels(Canvas canvas, Size size, double baselineY) {
    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      final x = i * columnWidth + columnWidth / 2;
      
      final textPainter = TextPainter(
        text: TextSpan(
          text: point.label,
          style: GoogleFonts.inter(
            fontSize: em * 0.5, // REFINEMENT: Further reduced from 0.6 to 0.5 for more compact labels
            color: point.isFuture 
                ? theme.colorScheme.onSurfaceVariant.withOpacity(0.6)
                : theme.colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.normal,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      
      textPainter.layout();
      
      final labelY = baselineY + em * 1.2;
      final labelX = x - textPainter.width / 2;
      
      textPainter.paint(canvas, Offset(labelX, labelY));
    }
  }

  @override
  bool shouldRepaint(covariant _InfiniteChartPainter oldDelegate) {
    return oldDelegate.dataPoints != dataPoints ||
           oldDelegate.columnWidth != columnWidth ||
           oldDelegate.chartHeight != chartHeight ||
           oldDelegate.em != em;
  }
}