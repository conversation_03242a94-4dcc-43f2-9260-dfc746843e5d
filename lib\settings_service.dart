import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

/// Service for managing user settings and preferences
class SettingsService {
  static const String _startOfWeekKey = 'start_of_week';
  static const String _themeKey = 'theme_mode';
  static const String _showCompletedKey = 'show_completed_habits';
  
  // Week start constants
  static const int SUNDAY = 7;
  static const int MONDAY = 1;
  
  static SettingsService? _instance;
  SharedPreferences? _prefs;
  bool _isInitialized = false;
  
  SettingsService._();
  
  static SettingsService get instance {
    _instance ??= SettingsService._();
    return _instance!;
  }
  
  /// Initialize the settings service
  Future<void> initialize() async {
    try {
      debugPrint('[SETTINGS_SERVICE] === INITIALIZING SETTINGS SERVICE ===');
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
      debugPrint('[SETTINGS_SERVICE] Successfully initialized with SharedPreferences');
      debugPrint('[SETTINGS_SERVICE] Available keys: ${_prefs!.getKeys()}');
    } catch (e, stackTrace) {
      debugPrint('[SETTINGS_SERVICE] ERROR: Failed to initialize SharedPreferences');
      debugPrint('[SETTINGS_SERVICE] Error: $e');
      debugPrint('[SETTINGS_SERVICE] StackTrace: $stackTrace');
      _isInitialized = false;
      rethrow;
    }
  }
  
  /// Get the user's preferred start of week
  /// Returns SUNDAY (7) or MONDAY (1)
  /// Defaults to SUNDAY if no preference is set
  Future<int> getStartOfWeek() async {
    try {
      debugPrint('[SETTINGS_SERVICE] === GETTING START OF WEEK ===');
      await _ensureInitialized();
      
      if (!_isInitialized || _prefs == null) {
        debugPrint('[SETTINGS_SERVICE] WARNING: Not initialized, returning default SUNDAY');
        return SUNDAY;
      }
      
      final startOfWeek = _prefs!.getInt(_startOfWeekKey) ?? SUNDAY;
      debugPrint('[SETTINGS_SERVICE] Retrieved start of week: ${startOfWeek == SUNDAY ? 'Sunday' : 'Monday'} (value: $startOfWeek)');
      debugPrint('[SETTINGS_SERVICE] All stored keys: ${_prefs!.getKeys()}');
      return startOfWeek;
    } catch (e, stackTrace) {
      debugPrint('[SETTINGS_SERVICE] ERROR: Failed to get start of week');
      debugPrint('[SETTINGS_SERVICE] Error: $e');
      debugPrint('[SETTINGS_SERVICE] StackTrace: $stackTrace');
      debugPrint('[SETTINGS_SERVICE] Returning default SUNDAY due to error');
      return SUNDAY;
    }
  }
  
  /// Set the user's preferred start of week
  /// @param day: SUNDAY (7) or MONDAY (1)
  Future<void> setStartOfWeek(int day) async {
    try {
      debugPrint('[SETTINGS_SERVICE] === SETTING START OF WEEK ===');
      debugPrint('[SETTINGS_SERVICE] Attempting to set start of week to: ${day == SUNDAY ? 'Sunday' : 'Monday'} (value: $day)');
      
      await _ensureInitialized();
      
      if (day != SUNDAY && day != MONDAY) {
        final error = 'Start of week must be SUNDAY (7) or MONDAY (1), got: $day';
        debugPrint('[SETTINGS_SERVICE] ERROR: $error');
        throw ArgumentError(error);
      }
      
      if (!_isInitialized || _prefs == null) {
        final error = 'Settings service not properly initialized';
        debugPrint('[SETTINGS_SERVICE] ERROR: $error');
        throw StateError(error);
      }
      
      final success = await _prefs!.setInt(_startOfWeekKey, day);
      debugPrint('[SETTINGS_SERVICE] Set operation result: $success');
      debugPrint('[SETTINGS_SERVICE] Successfully set start of week to: ${day == SUNDAY ? 'Sunday' : 'Monday'}');
      
      // Verify the setting was saved
      final verification = _prefs!.getInt(_startOfWeekKey);
      debugPrint('[SETTINGS_SERVICE] Verification read: $verification');
      
      if (verification != day) {
        debugPrint('[SETTINGS_SERVICE] WARNING: Verification failed! Expected $day, got $verification');
      }
    } catch (e, stackTrace) {
      debugPrint('[SETTINGS_SERVICE] ERROR: Failed to set start of week');
      debugPrint('[SETTINGS_SERVICE] Error: $e');
      debugPrint('[SETTINGS_SERVICE] StackTrace: $stackTrace');
      rethrow;
    }
  }
  
  /// Get the display name for a start of week value
  String getStartOfWeekDisplayName(int day) {
    switch (day) {
      case SUNDAY:
        return 'Sunday';
      case MONDAY:
        return 'Monday';
      default:
        return 'Unknown';
    }
  }
  
  /// Get theme mode preference
  Future<String> getThemeMode() async {
    await _ensureInitialized();
    return _prefs!.getString(_themeKey) ?? 'system';
  }
  
  /// Set theme mode preference
  Future<void> setThemeMode(String mode) async {
    await _ensureInitialized();
    await _prefs!.setString(_themeKey, mode);
    debugPrint('[SETTINGS_SERVICE] Set theme mode to: $mode');
  }
  
  /// Save theme preference (boolean version for ThemeNotifier)
  Future<void> saveTheme(bool isDarkMode) async {
    await _ensureInitialized();
    final mode = isDarkMode ? 'dark' : 'light';
    await _prefs!.setString(_themeKey, mode);
    debugPrint('[SETTINGS_SERVICE] Saved theme: $mode');
  }
  
  /// Load theme preference (boolean version for ThemeNotifier)
  Future<bool> loadTheme() async {
    await _ensureInitialized();
    final mode = _prefs!.getString(_themeKey) ?? 'system';
    final isDarkMode = mode == 'dark';
    debugPrint('[SETTINGS_SERVICE] Loaded theme: $mode (isDark: $isDarkMode)');
    return isDarkMode;
  }
  
  /// Save show completed habits preference
  Future<void> saveShowCompleted(bool showCompleted) async {
    await _ensureInitialized();
    await _prefs!.setBool(_showCompletedKey, showCompleted);
    debugPrint('[SETTINGS_SERVICE] Saved show completed: $showCompleted');
  }
  
  /// Load show completed habits preference
  Future<bool> loadShowCompleted() async {
    await _ensureInitialized();
    final showCompleted = _prefs!.getBool(_showCompletedKey) ?? false;
    debugPrint('[SETTINGS_SERVICE] Loaded show completed: $showCompleted');
    return showCompleted;
  }
  
  /// Ensure SharedPreferences is initialized
  Future<void> _ensureInitialized() async {
    try {
      debugPrint('[SETTINGS_SERVICE] === ENSURING INITIALIZATION ===');
      debugPrint('[SETTINGS_SERVICE] Current state - isInitialized: $_isInitialized, prefs: ${_prefs != null}');
      
      if (!_isInitialized || _prefs == null) {
        debugPrint('[SETTINGS_SERVICE] Not initialized, calling initialize()');
        await initialize();
      } else {
        debugPrint('[SETTINGS_SERVICE] Already initialized');
      }
      
      debugPrint('[SETTINGS_SERVICE] Final state - isInitialized: $_isInitialized, prefs: ${_prefs != null}');
    } catch (e, stackTrace) {
      debugPrint('[SETTINGS_SERVICE] ERROR: Failed to ensure initialization');
      debugPrint('[SETTINGS_SERVICE] Error: $e');
      debugPrint('[SETTINGS_SERVICE] StackTrace: $stackTrace');
      rethrow;
    }
  }
  
  /// Clear all settings (for testing purposes)
  Future<void> clearAll() async {
    await _ensureInitialized();
    await _prefs!.clear();
    debugPrint('[SETTINGS_SERVICE] Cleared all settings');
  }
}