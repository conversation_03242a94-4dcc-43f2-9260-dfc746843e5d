import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:two_dimensional_scrollables/two_dimensional_scrollables.dart';
import 'habit.dart';
import 'section.dart';
import 'status_indicator.dart';
import 'database_service.dart';
import 'table_row_data.dart';

class HabitTableView extends StatefulWidget {
  final List<Habit> habits;
  final List<DateTime> dates;
  final List<Section> sections;
  final Function(int oldIndex, int newIndex)? onReorder;
  final bool showReorderDialog;
  final bool showPercentageRow;
  final VoidCallback? onDataChanged;

  const HabitTableView({
    super.key,
    required this.habits,
    required this.dates,
    required this.sections,
    this.onReorder,
    this.showReorderDialog = true,
    this.showPercentageRow = true,
    this.onDataChanged,
  });

  @override
  State<HabitTableView> createState() => _HabitTableViewState();
}

class _HabitTableViewState extends State<HabitTableView> {
  final _databaseService = DatabaseService();
  final List<TableRowData> _flatList = [];
  final ScrollController _verticalController = ScrollController();
  final ScrollController _horizontalController = ScrollController();

  @override
  void initState() {
    super.initState();
    _updateFlatList();
  }

  @override
  void didUpdateWidget(HabitTableView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.habits != widget.habits) {
      _updateFlatList();
    }
  }

  @override
  void dispose() {
    _verticalController.dispose();
    _horizontalController.dispose();
    super.dispose();
  }

  void _updateFlatList() {
    _flatList.clear();
    for (int habitIndex = 0; habitIndex < widget.habits.length; habitIndex++) {
      final habit = widget.habits[habitIndex];
      Section? parentSection;
      if (habit.sectionIds.isNotEmpty && widget.sections.isNotEmpty) {
        parentSection = widget.sections.firstWhere(
          (section) => section.id == habit.sectionIds.first,
          orElse: () => Section(name: 'Unknown'),
        );
      } else {
        parentSection = Section(name: 'Unknown');
      }
      _flatList.add(HabitDataRow(habit, parentSection));
    }
  }

  // DAILY PERCENTAGE CALCULATION: Helper function to calculate completion percentage for a given date
  int _calculateCompletionPercentage(DateTime date) {
    if (widget.habits.isEmpty) return 0; // Avoid division by zero

    int completedCount = 0;
    for (final habit in widget.habits) {
      // Check if the habit was completed on the given date
      if (habit.completions.containsKey(date) &&
          habit.completions[date] == true) {
        completedCount++;
      }
    }

    // Calculate percentage and return as integer
    return ((completedCount / widget.habits.length) * 100).round();
  }

  // ADAPTIVE LAYOUT CALCULATION: Helper function to calculate both height and font size
  ({double height, TextStyle style}) _calculateLayout(
    String text,
    BuildContext context,
  ) {
    final wordCount = text.split(' ').length;
    final baseStyle = Theme.of(context).textTheme.titleMedium!;

    // Step 1: Determine the font size based on word count - increased for readability
    double fontSize;
    if (wordCount > 6) {
      // More than 6 words
      fontSize = 13.0; // Increased for better readability
    } else if (wordCount > 3) {
      // 4 to 6 words
      fontSize = 14.0; // Increased for better readability
    } else {
      fontSize = 15.0; // Increased for better readability
    }

    final dynamicTextStyle = baseStyle.copyWith(fontSize: fontSize);

    // Step 2: Use TextPainter to calculate the exact height needed
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: dynamicTextStyle),
      maxLines: 2, // Allow up to two lines
      textDirection: TextDirection.ltr,
    )..layout(
        maxWidth: 140.0,
      ); // Use the fixed width of the first column - wider

    // Step 3: Return both the height and the style
    // Add more generous vertical padding to permanently fix clipping
    return (
      height: textPainter.height + 56.0,
      style: dynamicTextStyle,
    ); // Generous padding to prevent text clipping
  }

  @override
  Widget build(BuildContext context) {
    if (widget.habits.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.library_add_check_outlined,
              size: 60,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No habits yet',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Add habits to get started',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return _buildUnifiedTable();
  }

  Widget _buildUnifiedTable() {
    // ADAPTIVE LAYOUT SOLUTION: Pre-calculate layouts for all habits
    final Map<String, double> calculatedRowHeights = {};
    final Map<String, TextStyle> calculatedTextStyles = {};

    // Loop through the habits and calculate the layout for each one
    for (final habit in widget.habits) {
      final layout = _calculateLayout(habit.name, context);
      calculatedRowHeights[habit.id] = layout.height;
      calculatedTextStyles[habit.id] = layout.style;
    }

    // Start with fixed heights for the header rows - increased for readability
    final Map<int, TableSpanExtent> rowHeights = {
      0: const FixedTableSpanExtent(35), // Percentage Header - increased
      1: const FixedTableSpanExtent(55), // Date Header - increased
    };

    // Use the pre-calculated heights for each habit row
    for (int i = 0; i < widget.habits.length; i++) {
      final habit = widget.habits[i];
      final calculatedHeight = calculatedRowHeights[habit.id] ?? 60.0;
      rowHeights[i + 2] = FixedTableSpanExtent(calculatedHeight);
    }

    return TableView.builder(
      verticalDetails: ScrollableDetails.vertical(
        controller: _verticalController,
      ),
      horizontalDetails: ScrollableDetails.horizontal(
        controller: _horizontalController,
      ),
      columnCount: widget.dates.length + 1, // +1 for habit name column
      rowCount: _flatList.length + (widget.showPercentageRow ? 2 : 1), // +2 for percentage and date rows, or +1 for just date row
      pinnedRowCount: widget.showPercentageRow ? 2 : 1, // FREEZE both rows or just date header row
      pinnedColumnCount: 1, // FREEZE the habit name column
      columnBuilder: _buildColumnSpan,
      rowBuilder: (int index) {
        if (rowHeights.containsKey(index)) {
          return TableSpan(extent: rowHeights[index]!);
        } else {
          // Fallback for any unexpected rows
          return const TableSpan(extent: FixedTableSpanExtent(60));
        }
      },
      cellBuilder: (context, vicinity) {
        return _buildCellFlattened(
          context,
          vicinity,
          calculatedTextStyles,
        );
      },
    );
  }

  TableSpan _buildColumnSpan(int index) {
    if (index == 0) {
      // Habit name column - wider for better text display
      return const TableSpan(extent: FixedTableSpanExtent(140));
    } else {
      // Date columns - balanced to show ~5 columns on screen
      return const TableSpan(extent: FixedTableSpanExtent(55));
    }
  }

  TableViewCell _buildCellFlattened(
    BuildContext context,
    TableVicinity vicinity,
    Map<String, TextStyle> calculatedTextStyles,
  ) {
    final rowIndex = vicinity.row;
    final columnIndex = vicinity.column;
    
    // Handle header rows based on whether percentage row is shown
    if (widget.showPercentageRow) {
      // Traditional layout: percentage row (0), date row (1), then habits (2+)
      final flatListIndex = rowIndex - 2;
      
      if (rowIndex == 0) {
        return _buildPercentageCell(columnIndex);
      }
      if (rowIndex == 1) {
        return _buildDateHeaderCell(columnIndex);
      }
      
      // Handle habit rows
      if (flatListIndex >= 0 && flatListIndex < _flatList.length) {
        final rowData = _flatList[flatListIndex];
        return _buildHabitRow(rowData, columnIndex, calculatedTextStyles);
      }
    } else {
      // Clean layout: date row (0), then habits (1+)
      final flatListIndex = rowIndex - 1;
      
      if (rowIndex == 0) {
        return _buildDateHeaderCell(columnIndex);
      }
      
      // Handle habit rows
      if (flatListIndex >= 0 && flatListIndex < _flatList.length) {
        final rowData = _flatList[flatListIndex];
        return _buildHabitRow(rowData, columnIndex, calculatedTextStyles);
      }
    }

    return _buildErrorCell('Invalid cell');
  }

  TableViewCell _buildHabitRow(
    TableRowData rowData,
    int columnIndex,
    Map<String, TextStyle> calculatedTextStyles,
  ) {
    switch (rowData) {
      case HabitDataRow habitRow:
        if (columnIndex == 0) {
          return _buildHabitNameCell(habitRow.habit, calculatedTextStyles);
        } else {
          final dateIndex = columnIndex - 1;
          if (dateIndex >= 0 && dateIndex < widget.dates.length) {
            return _buildStatusCell(habitRow.habit, widget.dates[dateIndex]);
          }
        }
        break;
      case SectionHeaderRow sectionRow:
        if (columnIndex == 0) {
          return TableViewCell(
            child: _buildSectionHeaderCell(sectionRow.section),
          );
        }
        break;
    }
    return _buildErrorCell('Invalid habit row');
  }

  TableViewCell _buildPercentageCell(int columnIndex) {
    if (columnIndex == 0) {
      return TableViewCell(
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
          child: Text(
            '%',
            style: GoogleFonts.inter(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).textTheme.labelSmall?.color ??
                  const Color(0xFF9E9E9E),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    } else {
      // Calculate percentage for this date
      final dateIndex = columnIndex - 1;
      if (dateIndex >= 0 && dateIndex < widget.dates.length) {
        final date = widget.dates[dateIndex];
        final percentage = _calculateCompletionPercentage(date);

        return TableViewCell(
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              border: Border(
                right: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 3.0, vertical: 6.0),
            child: Text(
              '$percentage%',
              style: GoogleFonts.inter(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).textTheme.labelSmall?.color ??
                    const Color(0xFF9E9E9E),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      }
    }

    return _buildErrorCell('Invalid %');
  }

  TableViewCell _buildDateHeaderCell(int columnIndex) {
    if (columnIndex == 0) {
      return TableViewCell(
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
          child: Text(
            'Habit',
            style: GoogleFonts.inter(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).textTheme.titleMedium?.color ??
                  const Color(0xFFE0E0E0),
            ),
          ),
        ),
      );
    } else {
      final dateIndex = columnIndex - 1;
      if (dateIndex >= 0 && dateIndex < widget.dates.length) {
        final date = widget.dates[dateIndex];
        final isToday = _isSameDay(date, DateTime.now());

        return TableViewCell(
          child: Container(
            decoration: BoxDecoration(
              color: isToday
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : Theme.of(context).colorScheme.surface,
              border: Border(
                right: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 3.0, vertical: 6.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${date.day}',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: isToday ? FontWeight.w600 : FontWeight.w500,
                    color: isToday
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  _getWeekdayAbbr(date.weekday),
                  style: GoogleFonts.inter(
                    fontSize: 11,
                    fontWeight: FontWeight.w400,
                    color: Theme.of(context).textTheme.labelSmall?.color ??
                        const Color(0xFF9E9E9E),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }
    }

    return _buildErrorCell('Invalid date');
  }

  TableViewCell _buildHabitNameCell(
    Habit habit,
    Map<String, TextStyle> calculatedTextStyles,
  ) {
    return TableViewCell(
      child: GestureDetector(
        onLongPress: () {
          _showHabitManagementDialog(habit);
        },
        onTap: () {
          _showHabitDetailsDialog(habit);
        },
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
          child: Row(
            children: [
              Container(
                width: 3,
                height: 16,
                decoration: BoxDecoration(
                  color: _getHabitColor(habit),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  habit.name,
                  style: calculatedTextStyles[habit.id]?.copyWith(
                        color: Theme.of(context).textTheme.titleMedium?.color,
                      ) ??
                      GoogleFonts.inter(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).textTheme.titleMedium?.color ??
                            const Color(0xFFE0E0E0),
                      ),
                  softWrap: true,
                ),
              ),
              Icon(
                Icons.more_vert,
                size: 18,
                color: Theme.of(context).textTheme.labelSmall?.color ??
                    const Color(0xFF9E9E9E),
              ),
            ],
          ),
        ),
      ),
    );
  }

  TableViewCell _buildStatusCell(Habit habit, DateTime date) {
    final isToday = _isSameDay(date, DateTime.now());
    final isFuture = date.isAfter(DateTime.now());

    // Get actual completion status from habit data using DateTime key
    bool isCompleted =
        habit.completions.containsKey(date) && habit.completions[date] == true;

    HabitStatus status;
    if (isFuture) {
      status = HabitStatus.pending;
    } else {
      status = isCompleted ? HabitStatus.completed : HabitStatus.missed;
    }

    return TableViewCell(
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          border: Border(
            right: BorderSide(color: Theme.of(context).dividerColor, width: 1),
            bottom: BorderSide(color: Theme.of(context).dividerColor, width: 1),
          ),
        ),
        padding: const EdgeInsets.all(6.0),
        child: Center(
          child: InkWell(
            onTap: isFuture
                ? null
                : () {
                    _toggleHabitCompletion(habit, date);
                  },
            borderRadius: BorderRadius.circular(10),
            child: Container(
              padding: const EdgeInsets.all(3.0),
              child: StatusIndicator(status: status, isToday: isToday),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeaderCell(Section section) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        border: Border(
          right: BorderSide(color: Theme.of(context).dividerColor, width: 1),
          bottom: BorderSide(color: Theme.of(context).dividerColor, width: 1),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      child: Text(
        section.name,
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.onPrimary,
        ),
      ),
    );
  }

  TableViewCell _buildErrorCell(String message) {
    return TableViewCell(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.red,
          border: Border(
            right: BorderSide(color: Theme.of(context).dividerColor, width: 1),
            bottom: BorderSide(color: Theme.of(context).dividerColor, width: 1),
          ),
        ),
        padding: const EdgeInsets.all(8.0),
        child: Center(
          child: Text(
            message,
            style: const TextStyle(color: Colors.white, fontSize: 10),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  // INTERACTIVE FEATURE: Toggle habit completion status
  Future<void> _toggleHabitCompletion(Habit habit, DateTime date) async {
    try {
      HapticFeedback.lightImpact();

      // Check current completion status
      final currentStatus = habit.completions[date] ?? false;

      // Create mutable copy with DateTime keys
      final Map<DateTime, bool> updatedCompletions = Map<DateTime, bool>.from(
        habit.completions,
      );

      // Toggle the completion status for the specific date
      if (currentStatus) {
        updatedCompletions.remove(date); // Remove entry for false (saves space)
      } else {
        updatedCompletions[date] = true; // Set to true for completion
      }

      final updatedHabit = Habit(
        id: habit.id,
        name: habit.name,
        sectionIds: habit.sectionIds,
        completions: updatedCompletions,
      );

      // Update in database
      await _databaseService.updateHabit(updatedHabit);

      // THIS IS THE CRITICAL FIX: Notify parent to reload all data
      if (widget.onDataChanged != null) {
        widget.onDataChanged!();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update habit: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // INTERACTIVE FEATURE: Show habit management dialog (Edit/Delete/Reorder)
  Future<void> _showHabitManagementDialog(Habit habit) async {
    HapticFeedback.mediumImpact();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Manage "${habit.name}"'),
        content: const Text('What would you like to do with this habit?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          if (widget.showReorderDialog && widget.onReorder != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showReorderHabitsDialog();
              },
              child: const Text('Reorder'),
            ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showHabitDetailsDialog(habit);
            },
            child: const Text('Details'),
          ),
        ],
      ),
    );
  }

  // INTERACTIVE FEATURE: Show habit details dialog
  Future<void> _showHabitDetailsDialog(Habit habit) async {
    final completionCount = habit.completions.values
        .where((completed) => completed)
        .length;
    final totalDays = habit.completions.length;
    final completionRate = totalDays > 0
        ? (completionCount / totalDays * 100).round()
        : 0;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(habit.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Completion Rate: $completionRate%'),
            const SizedBox(height: 8),
            Text('Completed: $completionCount days'),
            Text('Total tracked: $totalDays days'),
            const SizedBox(height: 16),
            Text('Sections: ${_getSectionNames(habit)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  // DRAG-AND-DROP ALTERNATIVE: Show reorder habits dialog
  Future<void> _showReorderHabitsDialog() async {
    if (widget.habits.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Need at least 2 habits to reorder'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (widget.onReorder == null) return;

    // Create a working copy of the habits list for reordering
    List<Habit> reorderableHabits = List.from(widget.habits);

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Reorder Habits'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: ReorderableListView.builder(
              itemCount: reorderableHabits.length,
              onReorder: (oldIndex, newIndex) {
                setDialogState(() {
                  if (newIndex > oldIndex) {
                    newIndex -= 1;
                  }
                  final habit = reorderableHabits.removeAt(oldIndex);
                  reorderableHabits.insert(newIndex, habit);
                });
              },
              itemBuilder: (context, index) {
                final habit = reorderableHabits[index];
                return Card(
                  key: ValueKey(habit.id),
                  margin: const EdgeInsets.symmetric(vertical: 2),
                  child: ListTile(
                    leading: Container(
                      width: 4,
                      height: 20,
                      decoration: BoxDecoration(
                        color: _getHabitColor(habit),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    title: Text(
                      habit.name,
                      style: const TextStyle(fontSize: 16),
                    ),
                    subtitle: Text('Section: ${_getSectionNames(habit)}'),
                    trailing: const Icon(Icons.drag_handle),
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                // Apply the reordering by calling the callback
                for (int i = 0; i < reorderableHabits.length; i++) {
                  final originalIndex = widget.habits.indexWhere(
                    (h) => h.id == reorderableHabits[i].id,
                  );
                  if (originalIndex != i && widget.onReorder != null) {
                    widget.onReorder!(originalIndex, i);
                  }
                }

                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Habit order updated successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('Save Order'),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  Color _getHabitColor(Habit habit) {
    // Generate a color based on habit name hash
    final colors = [
      const Color(0xFF4F46E5), // indigo
      const Color(0xFF059669), // emerald
      const Color(0xFFDC2626), // red
      const Color(0xFFCA8A04), // yellow
      const Color(0xFF7C3AED), // violet
      const Color(0xFF0891B2), // cyan
    ];
    return colors[habit.name.hashCode % colors.length];
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  String _getWeekdayAbbr(int weekday) {
    const weekdays = ['MO', 'TU', 'WE', 'TH', 'FR', 'SA', 'SU'];
    return weekdays[weekday - 1];
  }

  // HELPER METHOD: Get section names for a habit
  String _getSectionNames(Habit habit) {
    if (habit.sectionIds.isEmpty) return 'No sections';

    final sectionNames = habit.sectionIds.map((sectionId) {
      final section = widget.sections.firstWhere(
        (s) => s.id == sectionId,
        orElse: () => Section(name: 'Unknown'),
      );
      return section.name;
    }).toList();

    return sectionNames.join(', ');
  }
}