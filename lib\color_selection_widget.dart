import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'section_color_palette.dart';

class ColorSelectionWidget extends StatefulWidget {
  final String? selectedColor;
  final List<String> usedColors;
  final ValueChanged<String> onColorSelected;
  final bool showUsedColorWarning;

  const ColorSelectionWidget({
    super.key,
    this.selectedColor,
    this.usedColors = const [],
    required this.onColorSelected,
    this.showUsedColorWarning = true,
  });

  @override
  State<ColorSelectionWidget> createState() => _ColorSelectionWidgetState();
}

class _ColorSelectionWidgetState extends State<ColorSelectionWidget> {
  String? _selectedColor;
  bool _showAlternatives = false;

  @override
  void initState() {
    super.initState();
    _selectedColor = widget.selectedColor;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkTheme = theme.brightness == Brightness.dark;
    final availableColors = SectionColorPalette.getColorsForTheme(isDarkTheme);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Section Color',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 12),
        
        // Color Grid
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: availableColors.map((sectionColor) {
            final isSelected = _selectedColor == sectionColor.hex;
            final isUsed = widget.usedColors.contains(sectionColor.hex) && !isSelected;
            
            return GestureDetector(
              onTap: () => _selectColor(sectionColor.hex),
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: sectionColor.color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected 
                        ? theme.colorScheme.onSurface
                        : Colors.transparent,
                    width: 3,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: sectionColor.color.withOpacity(0.3),
                      blurRadius: isSelected ? 8 : 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    if (isSelected)
                      const Center(
                        child: Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    if (isUsed)
                      Positioned(
                        top: 2,
                        right: 2,
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surface,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: theme.colorScheme.onSurface,
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            Icons.info,
                            size: 10,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
        
        // Used Color Warning
        if (_selectedColor != null && 
            widget.usedColors.contains(_selectedColor) && 
            widget.showUsedColorWarning)
          _buildUsedColorWarning(isDarkTheme),
      ],
    );
  }

  Widget _buildUsedColorWarning(bool isDarkTheme) {
    final theme = Theme.of(context);
    final alternatives = SectionColorPalette.getSimilarColors(
      _selectedColor!,
      widget.usedColors,
      isDarkTheme: isDarkTheme,
    );

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Color already in use',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          if (alternatives.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'Suggested alternatives:',
              style: GoogleFonts.inter(
                fontSize: 12,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: alternatives.map((hex) {
                final color = SectionColorPalette.hexToColor(hex);
                return GestureDetector(
                  onTap: () => _selectColor(hex),
                  child: Container(
                    width: 32,
                    height: 32,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: color.withOpacity(0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  void _selectColor(String hex) {
    setState(() {
      _selectedColor = hex;
    });
    widget.onColorSelected(hex);
  }
}

// Simple Color Chip Widget for displaying section colors
class SectionColorChip extends StatelessWidget {
  final String colorHex;
  final double size;
  final bool showBorder;

  const SectionColorChip({
    super.key,
    required this.colorHex,
    this.size = 16,
    this.showBorder = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkTheme = theme.brightness == Brightness.dark;
    final color = SectionColorPalette.getColorFromHex(colorHex, isDarkTheme: isDarkTheme);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        border: showBorder
            ? Border.all(
                color: theme.colorScheme.onSurface.withOpacity(0.3),
                width: 1,
              )
            : null,
      ),
    );
  }
}