<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Habit Tracker</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<style>
        body {
            font-family: 'Inter', sans-serif;
            min-height: max(884px, 100dvh);
        }
        .material-icons {
            font-family: 'Material Icons';
            font-weight: normal;
            font-style: normal;
            font-size: 20px;
            line-height: 1;
            letter-spacing: normal;
            text-transform: none;
            display: inline-block;
            white-space: nowrap;
            word-wrap: normal;
            direction: ltr;
            -webkit-font-feature-settings: 'liga';
            -webkit-font-smoothing: antialiased;
        }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-gray-50 text-gray-800">
<div class="max-w-md mx-auto">
<header class="flex items-center justify-between p-4 bg-white border-b border-gray-100">
<h1 class="text-xl font-semibold">Habits</h1>
<div class="flex items-center space-x-3">
<i class="material-icons text-gray-500">add</i>
<i class="material-icons text-gray-500">filter_list</i>
<i class="material-icons text-gray-500">more_vert</i>
</div>
</header>
<main class="bg-white">
<div class="grid grid-cols-8 px-3 py-2 border-b border-gray-100 sticky top-0 bg-white">
<div class="col-span-3"></div>
<div class="text-center border-l border-gray-100 px-1">
<div class="text-[10px] font-medium text-gray-400">36%</div>
<div class="text-[10px] font-medium text-gray-400">MON</div>
<div class="text-xs font-semibold">30</div>
</div>
<div class="text-center border-l border-gray-100 px-1">
<div class="text-[10px] font-medium text-gray-400">55%</div>
<div class="text-[10px] font-medium text-gray-400">TUE</div>
<div class="text-xs font-semibold">1</div>
</div>
<div class="text-center border-l border-gray-100 px-1">
<div class="text-[10px] font-medium text-gray-400">45%</div>
<div class="text-[10px] font-medium text-gray-400">WED</div>
<div class="text-xs font-semibold">2</div>
</div>
<div class="text-center bg-indigo-50 text-indigo-600 rounded-md p-1 border-l border-transparent">
<div class="text-[10px] font-medium">18%</div>
<div class="text-[10px] font-medium">THU</div>
<div class="text-xs font-semibold">3</div>
</div>
<div class="text-center border-l border-gray-100 px-1">
<div class="text-[10px] font-medium text-gray-400">0%</div>
<div class="text-[10px] font-medium text-gray-400">FRI</div>
<div class="text-xs font-semibold">4</div>
</div>
</div>
<div class="divide-y divide-gray-100">
<div class="grid grid-cols-8 items-center px-3 py-3">
<div class="col-span-3 flex items-center space-x-2">
<div class="w-1 h-5 bg-green-500 rounded-full"></div>
<span class="font-medium text-sm text-gray-700">Wake up early</span>
</div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center bg-indigo-50 rounded-md p-1.5 border-l border-transparent h-full items-center"><span class="w-2 h-2 rounded-full border border-indigo-600"></span></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><span class="w-2 h-2 rounded-full border border-gray-300"></span></div>
</div>
<div class="grid grid-cols-8 items-center px-3 py-3">
<div class="col-span-3 flex items-center space-x-2">
<div class="w-1 h-5 bg-green-400 rounded-full"></div>
<span class="font-medium text-sm text-gray-700">Cook healthy dinner</span>
</div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center bg-indigo-50 rounded-md p-1.5 border-l border-transparent h-full items-center"><span class="w-2 h-2 rounded-full border border-indigo-600"></span></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><span class="w-2 h-2 rounded-full border border-gray-300"></span></div>
</div>
<div class="grid grid-cols-8 items-center px-3 py-3">
<div class="col-span-3 flex items-center space-x-2">
<div class="w-1 h-5 bg-red-400 rounded-full"></div>
<span class="font-medium text-sm text-gray-700">Write journal</span>
</div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center bg-indigo-50 rounded-md p-1.5 border-l border-transparent h-full items-center"><span class="w-2 h-2 rounded-full border border-indigo-600"></span></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><span class="w-2 h-2 rounded-full border border-gray-300"></span></div>
</div>
<div class="grid grid-cols-8 items-center px-3 py-3">
<div class="col-span-3 flex items-center space-x-2">
<div class="w-1 h-5 bg-yellow-400 rounded-full"></div>
<span class="font-medium text-sm text-gray-700">Track time</span>
</div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center bg-indigo-50 rounded-md p-1.5 border-l border-transparent h-full items-center"><span class="w-2 h-2 rounded-full border border-indigo-600"></span></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><span class="w-2 h-2 rounded-full border border-gray-300"></span></div>
</div>
<div class="grid grid-cols-8 items-center px-3 py-3">
<div class="col-span-3 flex items-center space-x-2">
<div class="w-1 h-5 bg-blue-500 rounded-full"></div>
<span class="font-medium text-sm text-gray-700">Meditate</span>
</div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center bg-indigo-50 rounded-md p-1.5 border-l border-transparent h-full items-center"><span class="w-2 h-2 rounded-full border border-indigo-600"></span></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><span class="w-2 h-2 rounded-full border border-gray-300"></span></div>
</div>
<div class="grid grid-cols-8 items-center px-3 py-3">
<div class="col-span-3 flex items-center space-x-2">
<div class="w-1 h-5 bg-green-600 rounded-full"></div>
<span class="font-medium text-sm text-gray-700">Run</span>
</div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center bg-indigo-50 rounded-md p-1.5 border-l border-transparent h-full items-center"><span class="w-2 h-2 rounded-full border border-indigo-600"></span></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><span class="w-2 h-2 rounded-full border border-gray-300"></span></div>
</div>
<div class="grid grid-cols-8 items-center px-3 py-3">
<div class="col-span-3 flex items-center space-x-2">
<div class="w-1 h-5 bg-indigo-400 rounded-full"></div>
<span class="font-medium text-sm text-gray-700">Read books</span>
</div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center bg-indigo-50 rounded-md p-1.5 border-l border-transparent h-full items-center"><span class="w-2 h-2 rounded-full border border-indigo-600"></span></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><span class="w-2 h-2 rounded-full border border-gray-300"></span></div>
</div>
<div class="grid grid-cols-8 items-center px-3 py-3">
<div class="col-span-3 flex items-center space-x-2">
<div class="w-1 h-5 bg-red-400 rounded-full"></div>
<span class="font-medium text-sm text-gray-700">Learn French</span>
</div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center bg-indigo-50 rounded-md p-1.5 border-l border-transparent h-full items-center"><span class="w-2 h-2 rounded-full border border-indigo-600"></span></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><span class="w-2 h-2 rounded-full border border-gray-300"></span></div>
</div>
<div class="grid grid-cols-8 items-center px-3 py-3">
<div class="col-span-3 flex items-center space-x-2">
<div class="w-1 h-5 bg-blue-400 rounded-full"></div>
<span class="font-medium text-sm text-gray-700">Play chess</span>
</div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center bg-indigo-50 rounded-md p-1.5 border-l border-transparent h-full items-center"><span class="w-2 h-2 rounded-full border border-indigo-600"></span></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><span class="w-2 h-2 rounded-full border border-gray-300"></span></div>
</div>
<div class="grid grid-cols-8 items-center px-3 py-3">
<div class="col-span-3 flex items-center space-x-2">
<div class="w-1 h-5 bg-teal-400 rounded-full"></div>
<span class="font-medium text-sm text-gray-700">Practice guitar</span>
</div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-indigo-500">check</i></div>
<div class="flex justify-center bg-indigo-50 rounded-md p-1.5 border-l border-transparent h-full items-center"><span class="w-2 h-2 rounded-full border border-indigo-600"></span></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><span class="w-2 h-2 rounded-full border border-gray-300"></span></div>
</div>
<div class="grid grid-cols-8 items-center px-3 py-3">
<div class="col-span-3 flex items-center space-x-2">
<div class="w-1 h-5 bg-blue-300 rounded-full"></div>
<span class="font-medium text-sm text-gray-700">Call a friend</span>
</div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><i class="material-icons text-gray-300">close</i></div>
<div class="flex justify-center bg-indigo-50 rounded-md p-1.5 border-l border-transparent h-full items-center"><span class="w-2 h-2 rounded-full border border-indigo-600"></span></div>
<div class="flex justify-center border-l border-gray-100 h-full items-center"><span class="w-2 h-2 rounded-full border border-gray-300"></span></div>
</div>
</div>
</main>
</div>

</body></html>